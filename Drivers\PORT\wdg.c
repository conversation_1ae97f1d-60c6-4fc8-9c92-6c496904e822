#include "./PORT/port.h"

/**
 * @brief       初始化独立看门狗 
 * @param       prer: 分频数:0~7(只有低3位有效!)
 *   @arg       分频因子 = 4 * 2^prer. 但最大值只能是256!
 * @param       rlr: 自动重装载值,0~0XFFF. 
 * @note        时间计算(大概):Tout=((4 * 2^prer) * rlr) / 40 (ms). 
 * @note        等效于：10Tout = 2^prer * rlr 
 * @retval      无
 */
void iwdg_init(uint8_t prer, uint16_t rlr)
{
    IWDG->KR = 0X5555;  /* 使能对IWDG->PR和IWDG->RLR的写 */
    IWDG->PR = prer;    /* 设置分频系数 */
    IWDG->RLR = rlr;    /* 从加载寄存器 IWDG->RLR */
    IWDG->KR = 0XAAAA;  /* reload */
    IWDG->KR = 0XCCCC;  /* 使能看门狗 */
}

/**
 * @brief       喂独立看门狗
 * @param       无
 * @retval      无
 */
void iwdg_feed(void)
{
    IWDG->KR = 0XAAAA;  /* reload */
}

