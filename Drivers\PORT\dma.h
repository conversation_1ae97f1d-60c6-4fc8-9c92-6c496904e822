#ifndef __DMA_H_
#define __DMA_H_

extern void (*dma2_ch3_callback)(void); // DMA2通道3传输完成回调函数, 用户根据需要自行定义;

/**
 * @brief       DMA基本配置
 *   @note      这里仅对DMA完成一些基础性的配置, 包括: DMA时钟使能 / 设置外设地址 和 存储器地址
 *              其他配置参数(CCR寄存器), 需用户自己另外实现
 *
 * @param       dma_streamx : DMA及数据流, DMA1_Stream0 ~ DMA1_Stream7, DMA2_Stream0 ~ DMA1_Stream7
 * @param       ch          : 通道x, 0~7
 *                            具体某个外设对应哪个DMA, 哪个数据流, 哪个通道, 请参考<<STM32F4xx中文参考手册>> 9.3.3节
 *                            必须设置正确的DMA及通道, 才能正常使用!
 * @param       par         : 外设地址
 * @param       m0ar        : 存储器0地址
 * @param       m1ar        : 存储器1地址, 使用双缓存的时候才会用到
 * @retval      无
 */
void dma_basic_config(DMA_Stream_TypeDef *dma_streamx, uint32_t ch, uint32_t par, uint32_t m0ar, uint32_t m1ar);

/**
 * @brief       开启一次DMA传输
 * @param       dma_streamx : DMA数据流,DMA1_Stream0~7/DMA2_Stream0~7
 * @param       ndtr        : 数据传输量
 * @retval      无
 */
void dma_enable(DMA_Stream_TypeDef *dma_streamx, uint16_t ndtr);

#endif
