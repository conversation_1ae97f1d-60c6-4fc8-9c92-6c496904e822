#ifndef __APP_KEY_H_
#define __APP_KEY_H_

/**
 * @brief       启动按键处理任务
 * @param       无
 * @retval      1:失败 0:成功
 */
uint8_t app_key_task_start(void);

/**
 * @brief       停止按键处理任务
 * @param       无
 * @retval      无
 */
void app_key_task_stop(void);

/**
 * @brief       注册按键回调函数
 * @param       fun: 回调函数
 * @retval      无
 */
void app_key_callback_register(void (*fun)(uint8_t, uint8_t));

/**
 * @brief       注销按键回调函数
 * @param       fun: 回调函数
 * @retval      无
 */
void app_key_callback_unregister(void (*fun)(uint8_t, uint8_t));

#endif
