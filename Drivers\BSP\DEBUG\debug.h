#ifndef __DEBUG_H__
#define __DEBUG_H__

#include "./PORT/port.h"

#pragma diag_suppress 870 // 忽略中文字符串编译警告

// 使用宏来自动添加函数名前缀
#define DEBUG_ERROR(format, ...) debug_error(__func__, format, ##__VA_ARGS__)
#define DEBUG_INFO(format, ...) debug_message(__func__, format, ##__VA_ARGS__)
#define DEBUG_WARN(format, ...) debug_warning(__func__, format, ##__VA_ARGS__)


/**
 * @brief      获取当前时间
 * @param      无
 * @retval    无
 */
char *get_debug_time(void);

/**
 * @brief      发送数据
 * @param      data: 数据指针
 * @param      length: 数据长度
 * @retval     无
 */
void debug_send_data(uint8_t *data, uint16_t length);

/**
 * @brief      初始化
 * @param      无
 * @retval     无
 */
void debug_init(void);

/**
 * @brief      打印信息不带时间
 * @param      format: 格式化字符串
 * @param      ...: 可变参数
 * @retval     无
 */
void my_printf(char *format, ...);

/**
 * @brief      打印信息
 * @param      format: 格式化字符串
 * @param      ...: 可变参数
 * @retval     无
 */
void debug_printf(char *format, ...);

/**
 * @brief      打印错误信息
 * @param      TAG: 调用函数名
 * @param      format: 格式化字符串
 * @param      ...: 可变参数
 * @retval     无
 */
void debug_error(const char *TAG, char *format, ...);

/**
 * @brief      打印警告信息
 * @param      TAG: 调用函数名
 * @param      format: 格式化字符串
 * @param      ...: 可变参数
 * @retval     无
 */
void debug_warning(const char *TAG, char *format, ...);

/**
 * @brief      打印信息
 * @param      TAG: 调用函数名
 * @param      format: 格式化字符串
 * @param      ...: 可变参数
 * @retval     无
 */
void debug_message(const char *TAG, char *format, ...);


#endif
