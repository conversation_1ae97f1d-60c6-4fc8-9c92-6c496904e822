#ifndef __CONFIG_H__
#define __CONFIG_H__

// clang-format off

/*
STM32F407ZGT6引脚功能说明 网址：
https://blog.csdn.net/u011510016/article/details/100404880
*/

#define CFG_ENABLE      1
#define CFG_DISABLE     0

/************************ 调试串口配置 ************************/
#define CFG_DEBUG_EN            CFG_ENABLE
#define CFG_DEBUG_SYS_LOG       CFG_ENABLE      //系统状态打印
#define CFG_DEBUG_MODE          1       //0:usb x:uartx
#define CFG_DEBUG_UART_ID       4
#define CFG_DEBUG_UART_BAUD     1000000
#define CFG_DEBUG_UART_TX_PIN   PA0
#define CFG_DEBUG_UART_RX_PIN   PA1

/************************ USMART调试组件配置 ************************/
#define CFG_DEBUG_USMART_EN     CFG_ENABLE
#define CFG_DEBUG_USMART_TIM_ID 7       //暂不支持修改

/************************ LED状态指示灯 ************************/
#define CFG_LED_EN              CFG_ENABLE
#define CFG_LED_PIN             PB12, PB13, PB14, PB15 //状态指示灯引脚(对应下方顺序)
#define CFG_LED_POLARITY        0,0,0,0         //状态指示灯 亮灯电平(0:低电平 1:高电平)
#define CFG_LED_ON              0               //仅做宏使用，不作为配置
#define CFG_LED_OFF             1

/************************ GPIO按键 ************************/
#define CFG_GPIO_KEY_EN         CFG_ENABLE
#define CFG_GPIO_KEY_PIN        PC12, PC8, PC13, PD2, PD3, PE5 //按键引脚(对应下方顺序)
#define CFG_GPIO_KEY_VALUE      1,2,3,4,5,6     //按键值
#define CFG_GPIO_KEY_POLARITY   0,0,0,0,0,0     //按键按下时的电平(0:低电平 1:高电平)

/************************ ADC按键 ************************/
#define CFG_ADC_KEY_EN          CFG_DISABLE
#define CFG_ADC_KEY_ADC_ID      1
#define CFG_ADC_KEY_PIN         PA0
#define CFG_ADC_KEY_NUM         4
#define CFG_ADC_KEY_VALUE       1,2,3,4

#define CFG_ADC_KEY_VDDIO       (0xfffL) // 4095
#define CFG_ADC_KEY_VALUE_MAX   4095
#define CFG_ADC_KEY_R_UP        100     //上拉电阻 10K
#define CFG_ADC_KEY_R0          0       //0.0k
#define CFG_ADC_KEY_R1          62      //6.2k
#define CFG_ADC_KEY_R2          150     //15.0k
#define CFG_ADC_KEY_R3          240     //24.0k

#define CFG_ADC_KEY_ADC_0       (CFG_ADC_KEY_VDDIO * CFG_ADC_KEY_R0 / (CFG_ADC_KEY_R0 + CFG_ADC_KEY_R_UP)) // 0.0R       0
#define CFG_ADC_KEY_ADC_1       (CFG_ADC_KEY_VDDIO * CFG_ADC_KEY_R1 / (CFG_ADC_KEY_R1 + CFG_ADC_KEY_R_UP)) // 6.2k       1567
#define CFG_ADC_KEY_ADC_2       (CFG_ADC_KEY_VDDIO * CFG_ADC_KEY_R2 / (CFG_ADC_KEY_R2 + CFG_ADC_KEY_R_UP)) // 15.0k      2457
#define CFG_ADC_KEY_ADC_3       (CFG_ADC_KEY_VDDIO * CFG_ADC_KEY_R3 / (CFG_ADC_KEY_R3 + CFG_ADC_KEY_R_UP)) // 24.0k      2890

#define CFG_ADC_KEY_VOLTAGE0    ((CFG_ADC_KEY_ADC_0 + CFG_ADC_KEY_ADC_1) / 2) // 783
#define CFG_ADC_KEY_VOLTAGE1    ((CFG_ADC_KEY_ADC_1 + CFG_ADC_KEY_ADC_2) / 2) // 2012
#define CFG_ADC_KEY_VOLTAGE2    ((CFG_ADC_KEY_ADC_2 + CFG_ADC_KEY_ADC_3) / 2) // 2673
#define CFG_ADC_KEY_VOLTAGE3    ((CFG_ADC_KEY_ADC_3 + CFG_ADC_KEY_VDDIO) / 2) // 3492

#define CFG_ADC_KEY_ADC_VALUE   CFG_ADC_KEY_VOLTAGE0, CFG_ADC_KEY_VOLTAGE1, CFG_ADC_KEY_VOLTAGE2, CFG_ADC_KEY_VOLTAGE3

/************************ 软件IIC配置 ************************/
#define CFG_SOFT_IIC_NUM        3

/************************ OLED配置 ************************/
#define CFG_OLED_MODE           0 //0:软件IIC 1:硬件IIC 2:软件SPI 3:硬件SPI

#define CFG_OLED_IIC_ID         1
#define CFG_OLED_IIC_DELAY_US   1       //IIC延时，单位us
#define CFG_OLED_IIC_SDA        PC9     //PB9   PB15
#define CFG_OLED_IIC_SCL        PA8     //PB8   PB13

#define CFG_OLED_SPI_ID         2
#define CFG_OLED_SPI_MOSI       PB15
#define CFG_OLED_SPI_SCK        PB13
#define CFG_OLED_SPI_CS         PC5
#define CFG_OLED_SPI_RST        PC4
#define CFG_OLED_SPI_DC         PB12

/************************ 文件系统FATFS配置 ************************/
#define CFG_FATFS_EN            CFG_ENABLE
#define CFG_FATFS_LFN           3       //0:8.3 3:long name
#define CFG_FATFS_CODEPAGE      936     //437:英文  936:简体中文

/************************ 独立看门狗配置 ************************/
//超时时间计算公式：Tout = 2^prer * rlr / 10
#define CFG_IWDG_EN             CFG_DISABLE //建议调试时关闭，正常运行时打开
#define CFG_IWDG_TIMEOUT        1000    //看门狗超时时间，单位ms
#define CFG_IWDG_PRESCALER      0x02    //预分频值
#define CFG_IWDG_RELOAD         (10 * CFG_IWDG_TIMEOUT / (1 << CFG_IWDG_PRESCALER)) //自动重装值

/************************ 硬件定时器中断扫描函数配置 ************************/
#define CFG_TIM_INT_EN          CFG_ENABLE
#define CFG_TIM_INT_PERIOD      10          //定时器扫描间隔
#define CFG_TIM_INT_ID          6           //TIM6
#define CFG_TIM_INT_PSC         8400 - 1    //预分频值
#define CFG_TIM_INT_ARR         ( 10 * CFG_TIM_INT_PERIOD - 1) //自动重装值

/************************ 电机配置 ************************/
#define CFG_MOTOR_EN                    CFG_DISABLE
#define CFG_MOTOR_DRIVER                1                       //1:TB6612FNG 2:L298N 
#define CFG_MOTOR_NUM                   2                       //电机数量
#define CFG_MOTOR_PWM_TIM_ID            4, 4                    //电机 PWM 定时器ID
#define CFG_MOTOR_PWM_CHANNEL           1, 2                    //电机 PWM 通道
#define CFG_MOTOR_PWM_PIN               PD12, PD13              //电机 PWM 引脚
#define CFG_MOTOR_CONTROL_PIN           PD8, PD9, PD11, PD10    //电机控制引脚(两脚为一组)
#define CFG_MOTOR_FREQUENCY             10000                   //电机 PWM 频率，以84MHz为基准
#define CFG_MOTOR_WHEEL_DIAMETER_CM     (6.5f *3.14159265f)     //轮子直径，单位cm
#define CFG_MOTOR_PPR                   (500 * 28 * 4)          // 编码器每转脉冲数 13线/相, 20倍减速比, 4倍频  
#define CFG_MOTOR_DEAD_BAND             6, 6                    //电机死区 占空比

#define CFG_MOTOR_DIRECTION_STOP        0       //电机停止
#define CFG_MOTOR_DIRECTION_FORWARD     1       //电机正转
#define CFG_MOTOR_DIRECTION_BACKWARD    2       //电机反转

#define CFG_MOTOR_R_ID                  0       //右电机ID   
#define CFG_MOTOR_L_ID                  1       //左电机ID

/************************ 编码器配置 ************************/

#define CFG_ENCODER_EN          CFG_ENABLE
#define CFG_ENCODER_NUM         2                   //编码器数量
#define CFG_ENCODER_TIM_ID      1,3                 //编码器定时器ID
#define CFG_ENCODER_PIN         PE9,PE11,PA6,PA7,   //编码器引脚(两脚为一组)

/************************ 循迹模块配置 ************************/
#define CFG_TRACK_EN            CFG_DISABLE 
#define CFG_TRACK_IIC_ID        2
#define CFG_TRACK_IIC_DELAY_US  20
#define CFG_TRACK_IIC_SDA       PB11
#define CFG_TRACK_IIC_SCL       PB10

/************************ JYXX模块配置 ************************/
#define CFG_JYxx_EN            CFG_ENABLE
#define CFG_JYxx_MODULE         1       //1:JY60 2:JY90
#define CFG_JYxx_MODE           1       //1:串口 2:IIC  (暂不支持IIC模式)
#define CFG_JYxx_UART_ID        2        
#define CFG_JYxx_UART_BAUD      9600
#define CFG_JYxx_UART_TX_PIN    PD5
#define CFG_JYxx_UART_RX_PIN    PD6

/************************ 超声波模块配置 ************************/
#define CFG_ULTRASOUND_EN               CFG_ENABLE
#define CFG_ULTRASOUND_ECHO_TIM_ID          11                  // 回声引脚定时器号
#define CFG_ULTRASOUND_ECHO_TIM_CHANNEL     1                   // 回声引脚定时器通道
#define CFG_ULTRASOUND_ECHO_TIM_PSC         168-1               // 回声引脚定时器预分频PSC
#define CFG_ULTRASOUND_ECHO_TIM_ARR         65535               // 回声引脚定时器重装载ARR
#define CFG_ULTRASOUND_ECHO_PIN             PB9                 // 回声引脚
#define CFG_ULTRASOUND_TRIG_PIN             PB8                 // 触发引脚

// clang-format on

#endif /* __CONFIG_H__ */
