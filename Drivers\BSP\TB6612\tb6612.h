#ifndef __TB6612_H_
#define __TB6612_H_

/**
 * @brief       TB6612电机驱动初始化函数
 * @note        该函数会初始化TB6612控制引脚和PWM引脚
 * @retval      无
 */
void tb6612_init(void);

/**
 * @brief       设置TB6612电机驱动方向
 * @param       motor_id: 电机编号, 从1开始
 * @param       direction: 电机方向, 0:停止 1:正转, 2:反转
 * @retval      无
 */
void tb6612_set_direction(uint8_t motor_id, uint8_t direction);

/**
 * @brief       设置TB6612电机驱动PWM值
 * @param       motor_id: 电机编号, 从1开始
 * @param       value: 占空比 -> 0~100
 * @retval      无
 */
void tb6612_set_pwm_value(uint8_t motor_id, uint16_t value);

#endif
