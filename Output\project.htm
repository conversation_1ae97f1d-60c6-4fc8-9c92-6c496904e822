<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\Output\project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\Output\project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Thu Jul 24 22:39:07 2025
<BR><P>
<H3>Maximum Stack Usage =        496 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
app_ui_task &rArr; jyxx_oled_show &rArr; OLED_Printf &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[e]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e]">NMI_Handler</a><BR>
 <LI><a href="#[10]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[10]">MemManage_Handler</a><BR>
 <LI><a href="#[11]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[11]">BusFault_Handler</a><BR>
 <LI><a href="#[12]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[12]">UsageFault_Handler</a><BR>
 <LI><a href="#[14]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[14]">DebugMon_Handler</a><BR>
 <LI><a href="#[29]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[29]">ADC_IRQHandler</a><BR>
 <LI><a href="#[fb]">usmart_strcmp</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[fb]">usmart_strcmp</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[29]">ADC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[11]">BusFault_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2b]">CAN1_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2c]">CAN1_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2d]">CAN1_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2a]">CAN1_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[57]">CAN2_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[58]">CAN2_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[59]">CAN2_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[56]">CAN2_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[65]">DCMI_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[22]">DMA1_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[23]">DMA1_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[24]">DMA1_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[25]">DMA1_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[26]">DMA1_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[27]">DMA1_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[28]">DMA1_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[46]">DMA1_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[50]">DMA2_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[51]">DMA2_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[52]">DMA2_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[53]">DMA2_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5b]">DMA2_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5c]">DMA2_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5d]">DMA2_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[14]">DebugMon_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[54]">ETH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[55]">ETH_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1d]">EXTI0_IRQHandler</a> from gpio.o(i.EXTI0_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3f]">EXTI15_10_IRQHandler</a> from gpio.o(i.EXTI15_10_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1e]">EXTI1_IRQHandler</a> from gpio.o(i.EXTI1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1f]">EXTI2_IRQHandler</a> from gpio.o(i.EXTI2_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[20]">EXTI3_IRQHandler</a> from gpio.o(i.EXTI3_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[21]">EXTI4_IRQHandler</a> from gpio.o(i.EXTI4_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2e]">EXTI9_5_IRQHandler</a> from gpio.o(i.EXTI9_5_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1b]">FLASH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[47]">FMC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[67]">FPU_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[66]">HASH_RNG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[f]">HardFault_Handler</a> from sys.o(i.HardFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[37]">I2C1_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[36]">I2C1_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[39]">I2C2_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[38]">I2C2_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[60]">I2C3_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5f]">I2C3_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[10]">MemManage_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[e]">NMI_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5a]">OTG_FS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[41]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[62]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[61]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[64]">OTG_HS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[63]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[18]">PVD_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[15]">PendSV_Handler</a> from sys.o(i.PendSV_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1c]">RCC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[40]">RTC_Alarm_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1a]">RTC_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[d]">Reset_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[48]">SDIO_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3a]">SPI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3b]">SPI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4a]">SPI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[13]">SVC_Handler</a> from sys.o(i.SVC_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[16]">SysTick_Handler</a> from sys.o(i.SysTick_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[19]">TAMP_STAMP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2f]">TIM1_BRK_TIM9_IRQHandler</a> from tim.o(i.TIM1_BRK_TIM9_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[32]">TIM1_CC_IRQHandler</a> from tim.o(i.TIM1_CC_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[31]">TIM1_TRG_COM_TIM11_IRQHandler</a> from tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[30]">TIM1_UP_TIM10_IRQHandler</a> from tim.o(i.TIM1_UP_TIM10_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[33]">TIM2_IRQHandler</a> from tim.o(i.TIM2_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[34]">TIM3_IRQHandler</a> from tim.o(i.TIM3_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[35]">TIM4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[49]">TIM5_IRQHandler</a> from tim.o(i.TIM5_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4d]">TIM6_DAC_IRQHandler</a> from tim.o(i.TIM6_DAC_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4e]">TIM7_IRQHandler</a> from usmart_port.o(i.TIM7_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[42]">TIM8_BRK_TIM12_IRQHandler</a> from tim.o(i.TIM8_BRK_TIM12_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[45]">TIM8_CC_IRQHandler</a> from tim.o(i.TIM8_CC_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[44]">TIM8_TRG_COM_TIM14_IRQHandler</a> from tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[43]">TIM8_UP_TIM13_IRQHandler</a> from tim.o(i.TIM8_UP_TIM13_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4b]">UART4_IRQHandler</a> from usart.o(i.UART4_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4c]">UART5_IRQHandler</a> from usart.o(i.UART5_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3c]">USART1_IRQHandler</a> from usart.o(i.USART1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3d]">USART2_IRQHandler</a> from usart.o(i.USART2_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3e]">USART3_IRQHandler</a> from usart.o(i.USART3_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5e]">USART6_IRQHandler</a> from usart.o(i.USART6_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[12]">UsageFault_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[17]">WWDG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[69]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[6a]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[6a]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[6b]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0vsprintf)
 <LI><a href="#[6c]">app_key_task</a> from app_key.o(i.app_key_task) referenced from app_key.o(i.app_key_task_start)
 <LI><a href="#[6d]">app_state_task</a> from app_state.o(i.app_state_task) referenced from app_state.o(i.app_state_task_start)
 <LI><a href="#[6e]">app_ui_task</a> from app_ui.o(i.app_ui_task) referenced from app_ui.o(i.app_ui_task_start)
 <LI><a href="#[5]">delay_ms</a> from delay.o(i.delay_ms) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[6]">delay_us</a> from delay.o(i.delay_us) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[6f]">jy60_uart_recv_callback</a> from jy60.o(i.jy60_uart_recv_callback) referenced from jy60.o(i.jy60_init)
 <LI><a href="#[70]">key_scan</a> from key.o(i.key_scan) referenced from key.o(i.key_init)
 <LI><a href="#[68]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[8]">motor_set_speed</a> from motor.o(i.motor_set_speed) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[1]">my_mem_init</a> from malloc.o(i.my_mem_init) referenced 2 times from malloc.o(.data)
 <LI><a href="#[2]">my_mem_perused</a> from malloc.o(i.my_mem_perused) referenced 2 times from malloc.o(.data)
 <LI><a href="#[73]">prvIdleTask</a> from tasks.o(i.prvIdleTask) referenced from tasks.o(i.vTaskStartScheduler)
 <LI><a href="#[71]">prvTaskExitError</a> from port.o(i.prvTaskExitError) referenced from port.o(i.pxPortInitialiseStack)
 <LI><a href="#[74]">prvTimerTask</a> from timers.o(i.prvTimerTask) referenced from timers.o(i.xTimerCreateTimerTask)
 <LI><a href="#[3]">read_addr</a> from usmart.o(i.read_addr) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[7]">sys_soft_reset</a> from sys.o(i.sys_soft_reset) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[a]">usmart_cmd_rec</a> from usmart.o(i.usmart_cmd_rec) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[b]">usmart_exe</a> from usmart.o(i.usmart_exe) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[9]">usmart_init</a> from usmart.o(i.usmart_init) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[72]">usmart_recv_func</a> from usmart_port.o(i.usmart_recv_func) referenced from usmart.o(i.usmart_init)
 <LI><a href="#[c]">usmart_scan</a> from usmart.o(i.usmart_scan) referenced 2 times from usmart_config.o(.data)
 <LI><a href="#[4]">write_addr</a> from usmart.o(i.write_addr) referenced 2 times from usmart_config.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[69]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[117]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[75]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[88]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[118]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[119]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[11a]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[11b]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[11c]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[11d]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[94]"></a>vPortSVCHandler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>

<P><STRONG><a name="[10f]"></a>__asm___6_port_c_39a90d8d__prvStartFirstTask</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[10e]"></a>__asm___6_port_c_39a90d8d__prvEnableVFP</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[77]"></a>xPortPendSVHandler</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>

<P><STRONG><a name="[11e]"></a>vPortGetIPSR</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, port.o(.emb_text), UNUSED)

<P><STRONG><a name="[d]"></a>Reset_Handler</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[cc]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_uart_recv_callback
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[11f]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[120]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[7a]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[121]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[122]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[79]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_error
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
</UL>

<P><STRONG><a name="[cd]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_uart_recv_callback
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[123]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[7b]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[b7]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_error
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskListTasks
</UL>

<P><STRONG><a name="[92]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[109]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskListTasks
</UL>

<P><STRONG><a name="[7c]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[81]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[82]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[83]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[84]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[d0]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jyxx_oled_show
</UL>

<P><STRONG><a name="[9f]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[124]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[a2]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[85]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7d]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[125]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[86]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[126]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[7e]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[127]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[128]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[80]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[7f]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[76]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[129]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[12a]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[12b]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[1d]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gpio.o(i.EXTI0_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, gpio.o(i.EXTI15_10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = EXTI15_10_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gpio.o(i.EXTI1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gpio.o(i.EXTI2_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gpio.o(i.EXTI3_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gpio.o(i.EXTI4_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, gpio.o(i.EXTI9_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = EXTI9_5_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>HardFault_Handler</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, sys.o(i.HardFault_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HardFault_Handler &rArr; my_printf &rArr; usart_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>OLED_Init</STRONG> (Thumb, 618 bytes, Stack size 32 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = OLED_Init &rArr; i2c_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_data
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8d]"></a>OLED_Printf</STRONG> (Thumb, 42 bytes, Stack size 288 bytes, oled.o(i.OLED_Printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = OLED_Printf &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;track_oled_show
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jyxx_oled_show
</UL>

<P><STRONG><a name="[90]"></a>OLED_ShowChar</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OLED_ShowChar &rArr; OLED_ShowImage
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[91]"></a>OLED_ShowImage</STRONG> (Thumb, 308 bytes, Stack size 64 bytes, oled.o(i.OLED_ShowImage))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OLED_ShowImage
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[8f]"></a>OLED_ShowString</STRONG> (Thumb, 250 bytes, Stack size 64 bytes, oled.o(i.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Printf
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[93]"></a>OLED_Update</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, oled.o(i.OLED_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OLED_Update &rArr; i2c_write_data &rArr; i2c_wait_ack &rArr; i2c_stop &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_data
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[15]"></a>PendSV_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, sys.o(i.PendSV_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortPendSVHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>SVC_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, sys.o(i.SVC_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSVCHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>SysTick_Handler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sys.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = SysTick_Handler &rArr; xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM1_BRK_TIM9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM1_BRK_TIM9_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM1_CC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM1_CC_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = TIM1_TRG_COM_TIM11_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_tim_overflow_handler
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_tim_ic_handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM1_UP_TIM10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM1_UP_TIM10_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>TIM2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM2_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>TIM3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM3_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIM5_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM5_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, tim.o(i.TIM6_DAC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM6_DAC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>TIM7_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, usmart_port.o(i.TIM7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM7_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM8_BRK_TIM12_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM8_BRK_TIM12_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM8_CC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM8_CC_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM8_TRG_COM_TIM14_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, tim.o(i.TIM8_UP_TIM13_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM8_UP_TIM13_IRQHandler &rArr; tim_cap_irq_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_irq_callback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>UART4_IRQHandler</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, usart.o(i.UART4_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>UART5_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, usart.o(i.UART5_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>USART1_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, usart.o(i.USART1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>USART2_IRQHandler</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, usart.o(i.USART2_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>USART3_IRQHandler</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, usart.o(i.USART3_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>USART6_IRQHandler</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, usart.o(i.USART6_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[9a]"></a>__0snprintf</STRONG> (Thumb, 48 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[12c]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[b6]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_error
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_debug_time
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskListTasks
</UL>

<P><STRONG><a name="[12d]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[12e]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[9c]"></a>__0vsnprintf</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[12f]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[130]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[131]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[b8]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_error
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
</UL>

<P><STRONG><a name="[9d]"></a>__0vsprintf</STRONG> (Thumb, 30 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[132]"></a>__1vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[133]"></a>__2vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[134]"></a>__c89vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[8e]"></a>vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Printf
</UL>

<P><STRONG><a name="[100]"></a>__ARM_common_memclr4_10</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, usmart_str.o(i.__ARM_common_memclr4_10))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fparam
</UL>

<P><STRONG><a name="[135]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[136]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[137]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[a3]"></a>app_init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, main.o(i.app_init), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task_start
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task_start
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task_start
</UL>

<P><STRONG><a name="[a6]"></a>app_key_task_start</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, app_key.o(i.app_key_task_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = app_key_task_start &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_init
</UL>

<P><STRONG><a name="[a4]"></a>app_state_task_start</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, app_state.o(i.app_state_task_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = app_state_task_start &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_init
</UL>

<P><STRONG><a name="[a7]"></a>app_ui_task_start</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, app_ui.o(i.app_ui_task_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = app_ui_task_start &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_init
</UL>

<P><STRONG><a name="[b4]"></a>debug_error</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, debug.o(i.debug_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = debug_error &rArr; get_debug_time &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send_data
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_debug_time
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_int_callback_register
</UL>

<P><STRONG><a name="[ba]"></a>debug_init</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, debug.o(i.debug_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = debug_init &rArr; usart_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send_data
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ab]"></a>debug_message</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, debug.o(i.debug_message))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = debug_message &rArr; get_debug_time &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send_data
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_debug_time
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
</UL>

<P><STRONG><a name="[a5]"></a>debug_printf</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, debug.o(i.debug_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = debug_printf &rArr; get_debug_time &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send_data
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_debug_time
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_check_rst
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_init
</UL>

<P><STRONG><a name="[d9]"></a>delay_init</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, delay.o(i.delay_init))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5]"></a>delay_ms</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, delay.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_ms
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[6]"></a>delay_us</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, delay.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_read_distance
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[bc]"></a>encoder_init</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, encoder.o(i.encoder_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = encoder_init &rArr; tim_cnt_pin_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cnt_set_value
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cnt_pin_init
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b5]"></a>get_debug_time</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, debug.o(i.get_debug_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = get_debug_time &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_get_tick
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_error
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
</UL>

<P><STRONG><a name="[f7]"></a>gpio_af_set</STRONG> (Thumb, 102 bytes, Stack size 32 bytes, gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_pwm_pin_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cnt_pin_init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_pin_init
</UL>

<P><STRONG><a name="[d3]"></a>gpio_key_get_state</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gpio_key.o(i.gpio_key_get_state))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_get_value
</UL>

<P><STRONG><a name="[c0]"></a>gpio_key_init</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, gpio_key.o(i.gpio_key_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = gpio_key_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_init
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_init
</UL>

<P><STRONG><a name="[c2]"></a>gpio_key_scan</STRONG> (Thumb, 280 bytes, Stack size 16 bytes, gpio_key.o(i.gpio_key_scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = gpio_key_scan
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_read_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_scan
</UL>

<P><STRONG><a name="[c1]"></a>gpio_pin_init</STRONG> (Thumb, 384 bytes, Stack size 52 bytes, gpio.o(i.gpio_pin_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = gpio_pin_init
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_key_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_init
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_pwm_pin_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cnt_pin_init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_pin_init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tb6612_init
</UL>

<P><STRONG><a name="[c3]"></a>gpio_read_pin</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gpio.o(i.gpio_read_pin))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_key_scan
</UL>

<P><STRONG><a name="[d7]"></a>gpio_toggle_pin</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gpio.o(i.gpio_toggle_pin))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_toggle
</UL>

<P><STRONG><a name="[c5]"></a>gpio_write_pin</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gpio.o(i.gpio_write_pin))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_init
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_read_distance
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tb6612_set_direction
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tb6612_init
</UL>

<P><STRONG><a name="[8b]"></a>i2c_init</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, i2c.o(i.i2c_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = i2c_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_init
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;track_init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[c4]"></a>i2c_send_byte</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, i2c.o(i.i2c_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = i2c_send_byte &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_data
</UL>

<P><STRONG><a name="[c6]"></a>i2c_start</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, i2c.o(i.i2c_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = i2c_start &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_data
</UL>

<P><STRONG><a name="[c7]"></a>i2c_stop</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, i2c.o(i.i2c_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = i2c_stop &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_data
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
</UL>

<P><STRONG><a name="[c8]"></a>i2c_wait_ack</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, i2c.o(i.i2c_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = i2c_wait_ack &rArr; i2c_stop &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_read_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write_data
</UL>

<P><STRONG><a name="[8c]"></a>i2c_write_data</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, i2c.o(i.i2c_write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = i2c_write_data &rArr; i2c_wait_ack &rArr; i2c_stop &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[cf]"></a>jy60_get_acceleration</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, jy60.o(i.jy60_get_acceleration))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jyxx_oled_show
</UL>

<P><STRONG><a name="[d2]"></a>jy60_get_angle</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, jy60.o(i.jy60_get_angle))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jyxx_oled_show
</UL>

<P><STRONG><a name="[d1]"></a>jy60_get_angle_speed</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, jy60.o(i.jy60_get_angle_speed))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jyxx_oled_show
</UL>

<P><STRONG><a name="[c9]"></a>jy60_init</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, jy60.o(i.jy60_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = jy60_init &rArr; usart_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send_data
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_iqr_callback_register
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jyxx_init
</UL>

<P><STRONG><a name="[cb]"></a>jy60_send_command</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, jy60.o(i.jy60_send_command), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send_data
</UL>

<P><STRONG><a name="[ce]"></a>jyxx_init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, jyxx.o(i.jyxx_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = jyxx_init &rArr; jy60_init &rArr; usart_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_init
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b2]"></a>jyxx_oled_show</STRONG> (Thumb, 168 bytes, Stack size 48 bytes, jyxx.o(i.jyxx_oled_show))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = jyxx_oled_show &rArr; OLED_Printf &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Printf
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_get_angle_speed
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_get_angle
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_get_acceleration
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[aa]"></a>key_get_value</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, key.o(i.key_get_value))
<BR><BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_key_get_state
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
</UL>

<P><STRONG><a name="[d4]"></a>key_init</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, key.o(i.key_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = key_init &rArr; tim_int_callback_register &rArr; debug_error &rArr; get_debug_time &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_key_init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_int_callback_register
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[70]"></a>key_scan</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, key.o(i.key_scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = key_scan &rArr; gpio_key_scan
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_key_scan
</UL>
<BR>[Address Reference Count : 1]<UL><LI> key.o(i.key_init)
</UL>
<P><STRONG><a name="[d6]"></a>led_init</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, led.o(i.led_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = led_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_init
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[af]"></a>led_toggle</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, led.o(i.led_toggle))
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_toggle_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[68]"></a>main</STRONG> (Thumb, 152 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = main &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_stm32_clock_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_check_rst
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;track_init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_init
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task_start
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task_start
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task_start
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jyxx_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[dc]"></a>motor_init</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, motor.o(i.motor_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = motor_init &rArr; tb6612_init &rArr; tim_pwm_pin_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tb6612_init
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8]"></a>motor_set_speed</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, motor.o(i.motor_set_speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = motor_set_speed &rArr; tb6612_set_direction
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tb6612_set_pwm_value
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tb6612_set_direction
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[1]"></a>my_mem_init</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, malloc.o(i.my_mem_init))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> malloc.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>my_mem_perused</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, malloc.o(i.my_mem_perused))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>
<BR>[Address Reference Count : 1]<UL><LI> malloc.o(.data)
</UL>
<P><STRONG><a name="[89]"></a>my_printf</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, debug.o(i.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = my_printf &rArr; usart_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_send_data
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_scan
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[f2]"></a>pvPortMalloc</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, heap_4.o(i.pvPortMalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskListTasks
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[116]"></a>pxPortInitialiseStack</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, port.o(i.pxPortInitialiseStack))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[3]"></a>read_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usmart.o(i.read_addr))
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[da]"></a>sys_check_rst</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, sys.o(i.sys_check_rst))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = sys_check_rst &rArr; debug_printf &rArr; get_debug_time &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f3]"></a>sys_clock_set</STRONG> (Thumb, 270 bytes, Stack size 16 bytes, sys.o(i.sys_clock_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sys_clock_set
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_stm32_clock_init
</UL>

<P><STRONG><a name="[bf]"></a>sys_get_tick</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys.o(i.sys_get_tick))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_debug_time
</UL>

<P><STRONG><a name="[f8]"></a>sys_nvic_init</STRONG> (Thumb, 106 bytes, Stack size 12 bytes, sys.o(i.sys_nvic_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sys_nvic_init
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_int_callback_register
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_pin_init
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_timx_init
</UL>

<P><STRONG><a name="[7]"></a>sys_soft_reset</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys.o(i.sys_soft_reset))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[d8]"></a>sys_stm32_clock_init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, sys.o(i.sys_stm32_clock_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = sys_stm32_clock_init &rArr; sys_clock_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_clock_set
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[df]"></a>tb6612_init</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, tb6612.o(i.tb6612_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = tb6612_init &rArr; tim_pwm_pin_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_pwm_set_ccr
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_pwm_pin_init
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_init
</UL>

<P><STRONG><a name="[e0]"></a>tb6612_set_direction</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, tb6612.o(i.tb6612_set_direction))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = tb6612_set_direction
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_speed
</UL>

<P><STRONG><a name="[e1]"></a>tb6612_set_pwm_value</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, tb6612.o(i.tb6612_set_pwm_value))
<BR><BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_pwm_set_ccr
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_speed
</UL>

<P><STRONG><a name="[f9]"></a>tim_cap_get_value</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, tim.o(i.tim_cap_get_value))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_tim_ic_handler
</UL>

<P><STRONG><a name="[f6]"></a>tim_cap_pin_init</STRONG> (Thumb, 514 bytes, Stack size 40 bytes, tim.o(i.tim_cap_pin_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = tim_cap_pin_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_nvic_init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_init
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_init
</UL>

<P><STRONG><a name="[bd]"></a>tim_cnt_pin_init</STRONG> (Thumb, 570 bytes, Stack size 56 bytes, tim.o(i.tim_cnt_pin_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = tim_cnt_pin_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_init
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_init
</UL>

<P><STRONG><a name="[be]"></a>tim_cnt_set_value</STRONG> (Thumb, 116 bytes, Stack size 0 bytes, tim.o(i.tim_cnt_set_value))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_init
</UL>

<P><STRONG><a name="[d5]"></a>tim_int_callback_register</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, tim.o(i.tim_int_callback_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = tim_int_callback_register &rArr; debug_error &rArr; get_debug_time &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_error
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_nvic_init
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_init
</UL>

<P><STRONG><a name="[f4]"></a>tim_pwm_pin_init</STRONG> (Thumb, 478 bytes, Stack size 40 bytes, tim.o(i.tim_pwm_pin_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = tim_pwm_pin_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_init
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tb6612_init
</UL>

<P><STRONG><a name="[f5]"></a>tim_pwm_set_ccr</STRONG> (Thumb, 144 bytes, Stack size 0 bytes, tim.o(i.tim_pwm_set_ccr))
<BR><BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tb6612_set_pwm_value
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tb6612_init
</UL>

<P><STRONG><a name="[db]"></a>track_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, track.o(i.track_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = track_init &rArr; i2c_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_init
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b3]"></a>track_oled_show</STRONG> (Thumb, 72 bytes, Stack size 40 bytes, track.o(i.track_oled_show))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = track_oled_show &rArr; OLED_Printf &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Printf
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
</UL>

<P><STRONG><a name="[dd]"></a>ultrasound_init</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, ultrasound.o(i.ultrasound_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = ultrasound_init &rArr; tim_cap_pin_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_pin_init
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ae]"></a>ultrasound_read_distance</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, ultrasound.o(i.ultrasound_read_distance))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ultrasound_read_distance &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_write_pin
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[98]"></a>ultrasound_tim_ic_handler</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, ultrasound.o(i.ultrasound_tim_ic_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ultrasound_tim_ic_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tim_cap_get_value
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_TRG_COM_TIM11_IRQHandler
</UL>

<P><STRONG><a name="[99]"></a>ultrasound_tim_overflow_handler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ultrasound.o(i.ultrasound_tim_overflow_handler))
<BR><BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_TRG_COM_TIM11_IRQHandler
</UL>

<P><STRONG><a name="[bb]"></a>usart_init</STRONG> (Thumb, 316 bytes, Stack size 32 bytes, usart.o(i.usart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = usart_init &rArr; gpio_pin_init
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_nvic_init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_pin_init
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_init
</UL>

<P><STRONG><a name="[ca]"></a>usart_iqr_callback_register</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usart.o(i.usart_iqr_callback_register))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_init
</UL>

<P><STRONG><a name="[b9]"></a>usart_send_data</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, usart.o(i.usart_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usart_send_data
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_error
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_send_command
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy60_init
</UL>

<P><STRONG><a name="[a]"></a>usmart_cmd_rec</STRONG> (Thumb, 172 bytes, Stack size 104 bytes, usmart.o(i.usmart_cmd_rec))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = usmart_cmd_rec &rArr; usmart_get_fparam
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcmp
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fparam
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fname
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>usmart_exe</STRONG> (Thumb, 538 bytes, Stack size 136 bytes, usmart.o(i.usmart_exe))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = usmart_exe &rArr; my_printf &rArr; usart_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_timx_reset_time
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_timx_get_time
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_parmpos
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fname
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[105]"></a>usmart_get_aparm</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, usmart_str.o(i.usmart_get_aparm))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usmart_get_aparm
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
</UL>

<P><STRONG><a name="[104]"></a>usmart_get_cmdname</STRONG> (Thumb, 66 bytes, Stack size 4 bytes, usmart_str.o(i.usmart_get_cmdname))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = usmart_get_cmdname
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
</UL>

<P><STRONG><a name="[fa]"></a>usmart_get_fname</STRONG> (Thumb, 426 bytes, Stack size 60 bytes, usmart_str.o(i.usmart_get_fname))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = usmart_get_fname
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_cmd_rec
</UL>

<P><STRONG><a name="[fc]"></a>usmart_get_fparam</STRONG> (Thumb, 768 bytes, Stack size 256 bytes, usmart_str.o(i.usmart_get_fparam))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = usmart_get_fparam
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_common_memclr4_10
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_cmd_rec
</UL>

<P><STRONG><a name="[102]"></a>usmart_get_input_string</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usmart_port.o(i.usmart_get_input_string))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_scan
</UL>

<P><STRONG><a name="[fd]"></a>usmart_get_parmpos</STRONG> (Thumb, 84 bytes, Stack size 12 bytes, usmart_str.o(i.usmart_get_parmpos))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usmart_get_parmpos
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
</UL>

<P><STRONG><a name="[9]"></a>usmart_init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usmart.o(i.usmart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usmart_init &rArr; usmart_timx_init &rArr; sys_nvic_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_iqr_callback_register
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_timx_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[72]"></a>usmart_recv_func</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, usmart_port.o(i.usmart_recv_func))
<BR>[Address Reference Count : 1]<UL><LI> usmart.o(i.usmart_init)
</UL>
<P><STRONG><a name="[c]"></a>usmart_scan</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, usmart.o(i.usmart_scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = usmart_scan &rArr; usmart_sys_cmd_exe &rArr; my_printf &rArr; usart_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_input_string
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[106]"></a>usmart_str2num</STRONG> (Thumb, 274 bytes, Stack size 20 bytes, usmart_str.o(i.usmart_str2num))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usmart_str2num
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
</UL>

<P><STRONG><a name="[fb]"></a>usmart_strcmp</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, usmart_str.o(i.usmart_strcmp))
<BR><BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcmp
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_sys_cmd_exe
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_cmd_rec
</UL>

<P><STRONG><a name="[103]"></a>usmart_sys_cmd_exe</STRONG> (Thumb, 1488 bytes, Stack size 72 bytes, usmart.o(i.usmart_sys_cmd_exe))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = usmart_sys_cmd_exe &rArr; my_printf &rArr; usart_send_data
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_strcmp
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_str2num
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_fname
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_cmdname
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_get_aparm
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_scan
</UL>

<P><STRONG><a name="[ff]"></a>usmart_timx_get_time</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, usmart_port.o(i.usmart_timx_get_time))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
</UL>

<P><STRONG><a name="[101]"></a>usmart_timx_init</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, usmart_port.o(i.usmart_timx_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usmart_timx_init &rArr; sys_nvic_init
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_nvic_init
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_init
</UL>

<P><STRONG><a name="[fe]"></a>usmart_timx_reset_time</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, usmart_port.o(i.usmart_timx_reset_time))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usmart_exe
</UL>

<P><STRONG><a name="[e3]"></a>uxListRemove</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, list.o(i.uxListRemove))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>

<P><STRONG><a name="[112]"></a>vListInitialise</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, list.o(i.vListInitialise))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[115]"></a>vListInitialiseItem</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, list.o(i.vListInitialiseItem))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[e4]"></a>vListInsert</STRONG> (Thumb, 58 bytes, Stack size 4 bytes, list.o(i.vListInsert))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>

<P><STRONG><a name="[108]"></a>vListInsertEnd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, list.o(i.vListInsertEnd))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
</UL>

<P><STRONG><a name="[e5]"></a>vPortEnterCritical</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, port.o(i.vPortEnterCritical))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvListTasksWithinSingleList
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[e6]"></a>vPortExitCritical</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, port.o(i.vPortExitCritical))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvListTasksWithinSingleList
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[e7]"></a>vPortFree</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, heap_4.o(i.vPortFree))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskListTasks
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[10d]"></a>vPortSetupTimerInterrupt</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, port.o(i.vPortSetupTimerInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[ee]"></a>vQueueWaitForMessageRestricted</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, queue.o(i.vQueueWaitForMessageRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = vQueueWaitForMessageRestricted &rArr; vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[ac]"></a>vTaskDelete</STRONG> (Thumb, 190 bytes, Stack size 16 bytes, tasks.o(i.vTaskDelete))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = vTaskDelete &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
</UL>

<P><STRONG><a name="[113]"></a>vTaskInternalSetTimeOutState</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tasks.o(i.vTaskInternalSetTimeOutState))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[b1]"></a>vTaskListTasks</STRONG> (Thumb, 358 bytes, Stack size 64 bytes, tasks.o(i.vTaskListTasks))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = vTaskListTasks &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvListTasksWithinSingleList
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[f1]"></a>vTaskMissedYield</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskMissedYield))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[10a]"></a>vTaskPlaceOnEventList</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, tasks.o(i.vTaskPlaceOnEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[107]"></a>vTaskPlaceOnEventListRestricted</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, tasks.o(i.vTaskPlaceOnEventListRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[de]"></a>vTaskStartScheduler</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, tasks.o(i.vTaskStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[eb]"></a>vTaskSuspendAll</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, tasks.o(i.vTaskSuspendAll))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[78]"></a>vTaskSwitchContext</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, tasks.o(i.vTaskSwitchContext))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortPendSVHandler
</UL>

<P><STRONG><a name="[4]"></a>write_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usmart.o(i.write_addr))
<BR>[Address Reference Count : 1]<UL><LI> usmart_config.o(.data)
</UL>
<P><STRONG><a name="[b0]"></a>xPortGetFreeHeapSize</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, heap_4.o(i.xPortGetFreeHeapSize))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
</UL>

<P><STRONG><a name="[10c]"></a>xPortStartScheduler</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, port.o(i.xPortStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = xPortStartScheduler
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSetupTimerInterrupt
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvEnableVFP
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvStartFirstTask
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[96]"></a>xPortSysTickHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, port.o(i.xPortSysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[111]"></a>xQueueGenericCreate</STRONG> (Thumb, 148 bytes, Stack size 32 bytes, queue.o(i.xQueueGenericCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[ed]"></a>xQueueReceive</STRONG> (Thumb, 272 bytes, Stack size 56 bytes, queue.o(i.xQueueReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = xQueueReceive &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[114]"></a>xTaskCheckForTimeOut</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, tasks.o(i.xTaskCheckForTimeOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskCheckForTimeOut
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[ad]"></a>xTaskCreate</STRONG> (Thumb, 390 bytes, Stack size 40 bytes, tasks.o(i.xTaskCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxPortInitialiseStack
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task_start
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task_start
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task_start
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[a9]"></a>xTaskDelayUntil</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, tasks.o(i.xTaskDelayUntil))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = xTaskDelayUntil &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
</UL>

<P><STRONG><a name="[95]"></a>xTaskGetSchedulerState</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetSchedulerState))
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[a8]"></a>xTaskGetTickCount</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetTickCount))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_ui_task
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_state_task
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_key_task
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[110]"></a>xTaskIncrementTick</STRONG> (Thumb, 362 bytes, Stack size 28 bytes, tasks.o(i.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>

<P><STRONG><a name="[f0]"></a>xTaskRemoveFromEventList</STRONG> (Thumb, 214 bytes, Stack size 8 bytes, tasks.o(i.xTaskRemoveFromEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = xTaskRemoveFromEventList
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[ec]"></a>xTaskResumeAll</STRONG> (Thumb, 322 bytes, Stack size 40 bytes, tasks.o(i.xTaskResumeAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskListTasks
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[10b]"></a>xTimerCreateTimerTask</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, timers.o(i.xTimerCreateTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[97]"></a>tim_cap_irq_callback</STRONG> (Thumb, 292 bytes, Stack size 24 bytes, tim.o(i.tim_cap_irq_callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tim_cap_irq_callback
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM8_UP_TIM13_IRQHandler
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM8_TRG_COM_TIM14_IRQHandler
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM8_CC_IRQHandler
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM8_BRK_TIM12_IRQHandler
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_IRQHandler
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_UP_TIM10_IRQHandler
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_TRG_COM_TIM11_IRQHandler
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_CC_IRQHandler
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_BRK_TIM9_IRQHandler
</UL>

<P><STRONG><a name="[6f]"></a>jy60_uart_recv_callback</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, jy60.o(i.jy60_uart_recv_callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = jy60_uart_recv_callback
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> jy60.o(i.jy60_init)
</UL>
<P><STRONG><a name="[6c]"></a>app_key_task</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, app_key.o(i.app_key_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = app_key_task &rArr; vTaskDelete &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sys_soft_reset
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_get_value
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_message
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_key.o(i.app_key_task_start)
</UL>
<P><STRONG><a name="[6d]"></a>app_state_task</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, app_state.o(i.app_state_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = app_state_task &rArr; vTaskListTasks &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_printf
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_toggle
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortGetFreeHeapSize
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskListTasks
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_mem_perused
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ultrasound_read_distance
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_state.o(i.app_state_task_start)
</UL>
<P><STRONG><a name="[6e]"></a>app_ui_task</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, app_ui.o(i.app_ui_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 496<LI>Call Chain = app_ui_task &rArr; jyxx_oled_show &rArr; OLED_Printf &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;track_oled_show
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Printf
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jyxx_oled_show
</UL>
<BR>[Address Reference Count : 1]<UL><LI> app_ui.o(i.app_ui_task_start)
</UL>
<P><STRONG><a name="[71]"></a>prvTaskExitError</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, port.o(i.prvTaskExitError))
<BR>[Address Reference Count : 1]<UL><LI> port.o(i.pxPortInitialiseStack)
</UL>
<P><STRONG><a name="[ef]"></a>prvUnlockQueue</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, queue.o(i.prvUnlockQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskMissedYield
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[e2]"></a>prvAddCurrentTaskToDelayedList</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, tasks.o(i.prvAddCurrentTaskToDelayedList))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskDelayUntil
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
</UL>

<P><STRONG><a name="[73]"></a>prvIdleTask</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, tasks.o(i.prvIdleTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = prvIdleTask &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Address Reference Count : 1]<UL><LI> tasks.o(i.vTaskStartScheduler)
</UL>
<P><STRONG><a name="[e8]"></a>prvListTasksWithinSingleList</STRONG> (Thumb, 242 bytes, Stack size 56 bytes, tasks.o(i.prvListTasksWithinSingleList))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = prvListTasksWithinSingleList
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskListTasks
</UL>

<P><STRONG><a name="[e9]"></a>prvProcessExpiredTimer</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, timers.o(i.prvProcessExpiredTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = prvProcessExpiredTimer &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[ea]"></a>prvSampleTimeNow</STRONG> (Thumb, 208 bytes, Stack size 32 bytes, timers.o(i.prvSampleTimeNow))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = prvSampleTimeNow &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[74]"></a>prvTimerTask</STRONG> (Thumb, 702 bytes, Stack size 16 bytes, timers.o(i.prvTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = prvTimerTask &rArr; xQueueReceive &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(i.xTimerCreateTimerTask)
</UL>
<P><STRONG><a name="[9e]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[9b]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
</UL>

<P><STRONG><a name="[a1]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[a0]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[6a]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 2]<UL><LI> printfa.o(i.__0snprintf)
<LI> printfa.o(i.__0vsnprintf)
</UL>
<P><STRONG><a name="[6b]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
