Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to sys.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to sys.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to sys.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to sys.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM1_BRK_TIM9_IRQHandler) for TIM1_BRK_TIM9_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM1_UP_TIM10_IRQHandler) for TIM1_UP_TIM10_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler) for TIM1_TRG_COM_TIM11_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM1_CC_IRQHandler) for TIM1_CC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to gpio.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM8_BRK_TIM12_IRQHandler) for TIM8_BRK_TIM12_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM8_UP_TIM13_IRQHandler) for TIM8_UP_TIM13_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler) for TIM8_TRG_COM_TIM14_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM8_CC_IRQHandler) for TIM8_CC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM5_IRQHandler) for TIM5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to tim.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usmart_port.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    gpio.o(i.EXTI0_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI15_10_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI1_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI2_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI3_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI4_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.EXTI9_5_IRQHandler) refers to gpio.o(.bss) for .bss
    gpio.o(i.gpio_af_set) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_ex_callback_deregister) refers to gpio.o(.bss) for .bss
    gpio.o(i.gpio_ex_callback_register) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    gpio.o(i.gpio_ex_callback_register) refers to gpio.o(.bss) for .bss
    gpio.o(i.gpio_nvic_ex_config) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    gpio.o(i.gpio_nvic_ex_config) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    gpio.o(i.gpio_nvic_ex_config) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_nvic_ex_config) refers to gpio.o(.bss) for .bss
    gpio.o(i.gpio_pin_init) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_read_pin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_toggle_pin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.gpio_write_pin) refers to gpio.o(.constdata) for .constdata
    sys.o(i.HardFault_Handler) refers to debug.o(i.my_printf) for my_printf
    sys.o(i.PendSV_Handler) refers to port.o(.emb_text) for xPortPendSVHandler
    sys.o(i.SVC_Handler) refers to port.o(.emb_text) for vPortSVCHandler
    sys.o(i.SysTick_Handler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    sys.o(i.SysTick_Handler) refers to port.o(i.xPortSysTickHandler) for xPortSysTickHandler
    sys.o(i.SysTick_Handler) refers to sys.o(.data) for .data
    sys.o(i.sys_check_rst) refers to debug.o(i.debug_printf) for debug_printf
    sys.o(i.sys_get_tick) refers to sys.o(.data) for .data
    sys.o(i.sys_stm32_clock_init) refers to sys.o(i.sys_clock_set) for sys_clock_set
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(.data) for .data
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    usart.o(i.UART4_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.UART5_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART2_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART3_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.USART6_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.usart_init) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    usart.o(i.usart_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    usart.o(i.usart_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.usart_iqr_callback_deregister) refers to usart.o(.bss) for .bss
    usart.o(i.usart_iqr_callback_register) refers to usart.o(.bss) for .bss
    i2c.o(i.i2c_ack) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_ack) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_ack) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_delay) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    i2c.o(i.i2c_init) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_nack) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_nack) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_nack) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_read_byte) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_read_byte) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_read_byte) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    i2c.o(i.i2c_read_byte) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_read_data) refers to i2c.o(i.i2c_start) for i2c_start
    i2c.o(i.i2c_read_data) refers to i2c.o(i.i2c_send_byte) for i2c_send_byte
    i2c.o(i.i2c_read_data) refers to i2c.o(i.i2c_wait_ack) for i2c_wait_ack
    i2c.o(i.i2c_read_data) refers to i2c.o(i.i2c_stop) for i2c_stop
    i2c.o(i.i2c_read_data) refers to debug.o(i.debug_printf) for debug_printf
    i2c.o(i.i2c_read_data) refers to i2c.o(i.i2c_read_byte) for i2c_read_byte
    i2c.o(i.i2c_send_byte) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_send_byte) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_send_byte) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_start) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_start) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_start) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_stop) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_stop) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_stop) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_wait_ack) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    i2c.o(i.i2c_wait_ack) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.i2c_wait_ack) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    i2c.o(i.i2c_wait_ack) refers to i2c.o(i.i2c_stop) for i2c_stop
    i2c.o(i.i2c_wait_ack) refers to i2c.o(.data) for .data
    i2c.o(i.i2c_write_data) refers to i2c.o(i.i2c_start) for i2c_start
    i2c.o(i.i2c_write_data) refers to i2c.o(i.i2c_send_byte) for i2c_send_byte
    i2c.o(i.i2c_write_data) refers to i2c.o(i.i2c_wait_ack) for i2c_wait_ack
    i2c.o(i.i2c_write_data) refers to i2c.o(i.i2c_stop) for i2c_stop
    dma.o(i.dma_basic_config) refers to delay.o(i.delay_ms) for delay_ms
    spi.o(i.spi_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    spi.o(i.spi_pin_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    adc.o(i.adc_get_value) refers to adc.o(.data) for .data
    adc.o(i.adc_get_value) refers to adc.o(.bss) for .bss
    adc.o(i.adc_pin_init) refers to debug.o(i.debug_error) for debug_error
    adc.o(i.adc_pin_init) refers to dma.o(i.dma_basic_config) for dma_basic_config
    adc.o(i.adc_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    adc.o(i.adc_pin_init) refers to dma.o(i.dma_enable) for dma_enable
    adc.o(i.adc_pin_init) refers to adc.o(.constdata) for .constdata
    adc.o(i.adc_pin_init) refers to adc.o(.bss) for .bss
    adc.o(i.adc_pin_init) refers to adc.o(.data) for .data
    tim.o(i.TIM1_BRK_TIM9_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM1_BRK_TIM9_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM1_CC_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM1_CC_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler) refers to ultrasound.o(i.ultrasound_tim_ic_handler) for ultrasound_tim_ic_handler
    tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler) refers to ultrasound.o(i.ultrasound_tim_overflow_handler) for ultrasound_tim_overflow_handler
    tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM1_UP_TIM10_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM1_UP_TIM10_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM2_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM3_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM3_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM5_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM5_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM6_DAC_IRQHandler) refers to tim.o(.data) for .data
    tim.o(i.TIM6_DAC_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM8_BRK_TIM12_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM8_BRK_TIM12_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM8_CC_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM8_CC_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.TIM8_UP_TIM13_IRQHandler) refers to tim.o(i.tim_cap_irq_callback) for tim_cap_irq_callback
    tim.o(i.TIM8_UP_TIM13_IRQHandler) refers to tim.o(.bss) for .bss
    tim.o(i.btimx_int_init) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    tim.o(i.tim_cap_get_value) refers to tim.o(.bss) for .bss
    tim.o(i.tim_cap_irq_callback) refers to tim.o(.bss) for .bss
    tim.o(i.tim_cap_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    tim.o(i.tim_cap_pin_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    tim.o(i.tim_cap_pin_init) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    tim.o(i.tim_cap_pin_init) refers to tim.o(.bss) for .bss
    tim.o(i.tim_cnt_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    tim.o(i.tim_cnt_pin_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    tim.o(i.tim_int_callback_deregister) refers to tim.o(.data) for .data
    tim.o(i.tim_int_callback_deregister) refers to tim.o(.bss) for .bss
    tim.o(i.tim_int_callback_register) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    tim.o(i.tim_int_callback_register) refers to debug.o(i.debug_error) for debug_error
    tim.o(i.tim_int_callback_register) refers to tim.o(.data) for .data
    tim.o(i.tim_int_callback_register) refers to tim.o(.constdata) for .constdata
    tim.o(i.tim_int_callback_register) refers to tim.o(.bss) for .bss
    tim.o(i.tim_pwm_pin_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    tim.o(i.tim_pwm_pin_init) refers to gpio.o(i.gpio_af_set) for gpio_af_set
    led.o(i.led_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    led.o(i.led_init) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    led.o(i.led_init) refers to led.o(.constdata) for .constdata
    led.o(i.led_set) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    led.o(i.led_set) refers to led.o(.constdata) for .constdata
    led.o(i.led_toggle) refers to gpio.o(i.gpio_toggle_pin) for gpio_toggle_pin
    led.o(i.led_toggle) refers to led.o(.constdata) for .constdata
    debug.o(i.debug_error) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_error) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_error) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_error) refers to strlen.o(.text) for strlen
    debug.o(i.debug_error) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_error) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_error) refers to debug.o(.bss) for .bss
    debug.o(i.debug_init) refers to usart.o(i.usart_init) for usart_init
    debug.o(i.debug_init) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_init) refers to strlen.o(.text) for strlen
    debug.o(i.debug_init) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_init) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_init) refers to debug.o(.bss) for .bss
    debug.o(i.debug_message) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_message) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_message) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_message) refers to strlen.o(.text) for strlen
    debug.o(i.debug_message) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_message) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_message) refers to debug.o(.bss) for .bss
    debug.o(i.debug_printf) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_printf) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_printf) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_printf) refers to strlen.o(.text) for strlen
    debug.o(i.debug_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_printf) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_printf) refers to debug.o(.bss) for .bss
    debug.o(i.debug_send_data) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_warning) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.debug_warning) refers to debug.o(i.get_debug_time) for get_debug_time
    debug.o(i.debug_warning) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.debug_warning) refers to strlen.o(.text) for strlen
    debug.o(i.debug_warning) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.debug_warning) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.debug_warning) refers to debug.o(.bss) for .bss
    debug.o(i.get_debug_time) refers to sys.o(i.sys_get_tick) for sys_get_tick
    debug.o(i.get_debug_time) refers to printfa.o(i.__0snprintf) for __2snprintf
    debug.o(i.get_debug_time) refers to debug.o(.bss) for .bss
    debug.o(i.my_printf) refers to memseta.o(.text) for __aeabi_memclr
    debug.o(i.my_printf) refers to strlen.o(.text) for strlen
    debug.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    debug.o(i.my_printf) refers to usart.o(i.usart_send_data) for usart_send_data
    debug.o(i.my_printf) refers to debug.o(.bss) for .bss
    oled.o(i.OLED_Clear) refers to memseta.o(.text) for __aeabi_memclr
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflti.o(.text) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to dadd.o(.text) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(.text) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to cdcmple.o(.text) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_GPIO_Init) refers to i2c.o(i.i2c_init) for i2c_init
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Init) refers to i2c.o(i.i2c_init) for i2c_init
    oled.o(i.OLED_Init) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_Init) refers to memseta.o(.text) for __aeabi_memclr
    oled.o(i.OLED_Init) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_IsInAngle) refers to dflti.o(.text) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(.text) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfixi.o(.text) for __aeabi_d2iz
    oled.o(i.OLED_Printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_SetCursor) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F6x8
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixui.o(.text) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dfltui.o(.text) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to dadd.o(.text) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.__hardfp_round) for __hardfp_round
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to strcmp.o(.text) for strcmp
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowString) refers to oled_data.o(.constdata) for OLED_CF16x16
    oled.o(i.OLED_Update) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_Update) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_UpdateArea) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_WriteCommand) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    oled.o(i.OLED_WriteData) refers to i2c.o(i.i2c_write_data) for i2c_write_data
    gpio_key.o(i.gpio_key_get_state) refers to gpio_key.o(.data) for .data
    gpio_key.o(i.gpio_key_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    gpio_key.o(i.gpio_key_init) refers to gpio_key.o(.constdata) for .constdata
    gpio_key.o(i.gpio_key_read) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    gpio_key.o(i.gpio_key_read) refers to gpio_key.o(.constdata) for .constdata
    gpio_key.o(i.gpio_key_scan) refers to gpio.o(i.gpio_read_pin) for gpio_read_pin
    gpio_key.o(i.gpio_key_scan) refers to gpio_key.o(.constdata) for .constdata
    gpio_key.o(i.gpio_key_scan) refers to gpio_key.o(.data) for .data
    key.o(i.key_get_value) refers to gpio_key.o(i.gpio_key_get_state) for gpio_key_get_state
    key.o(i.key_init) refers to gpio_key.o(i.gpio_key_init) for gpio_key_init
    key.o(i.key_init) refers to tim.o(i.tim_int_callback_register) for tim_int_callback_register
    key.o(i.key_init) refers to key.o(i.key_scan) for key_scan
    key.o(i.key_scan) refers to gpio_key.o(i.gpio_key_scan) for gpio_key_scan
    encoder.o(i.encoder_get_value) refers to tim.o(i.tim_cnt_get_value) for tim_cnt_get_value
    encoder.o(i.encoder_get_value) refers to tim.o(i.tim_cnt_set_value) for tim_cnt_set_value
    encoder.o(i.encoder_get_value) refers to encoder.o(.constdata) for .constdata
    encoder.o(i.encoder_init) refers to tim.o(i.tim_cnt_pin_init) for tim_cnt_pin_init
    encoder.o(i.encoder_init) refers to tim.o(i.tim_cnt_set_value) for tim_cnt_set_value
    encoder.o(i.encoder_init) refers to encoder.o(.constdata) for .constdata
    track.o(i.track_get_line_position_error) refers to i2c.o(i.i2c_read_data) for i2c_read_data
    track.o(i.track_get_line_position_error) refers to track.o(.data) for .data
    track.o(i.track_get_line_position_error) refers to track.o(.constdata) for .constdata
    track.o(i.track_get_sensor_data) refers to track.o(.data) for .data
    track.o(i.track_init) refers to i2c.o(i.i2c_init) for i2c_init
    track.o(i.track_oled_show) refers to oled.o(i.OLED_Printf) for OLED_Printf
    track.o(i.track_oled_show) refers to track.o(.data) for .data
    track.o(i.track_read_sensors) refers to i2c.o(i.i2c_read_data) for i2c_read_data
    track.o(i.track_read_sensors) refers to track.o(.data) for .data
    jy60.o(i.jy60_get_acceleration) refers to jy60.o(.bss) for .bss
    jy60.o(i.jy60_get_angle) refers to jy60.o(.bss) for .bss
    jy60.o(i.jy60_get_angle_speed) refers to jy60.o(.bss) for .bss
    jy60.o(i.jy60_get_temperature) refers to jy60.o(.data) for .data
    jy60.o(i.jy60_get_version) refers to jy60.o(.data) for .data
    jy60.o(i.jy60_get_voltage) refers to jy60.o(.data) for .data
    jy60.o(i.jy60_init) refers to usart.o(i.usart_init) for usart_init
    jy60.o(i.jy60_init) refers to usart.o(i.usart_send_data) for usart_send_data
    jy60.o(i.jy60_init) refers to usart.o(i.usart_iqr_callback_register) for usart_iqr_callback_register
    jy60.o(i.jy60_init) refers to jy60.o(i.jy60_send_command) for i.jy60_send_command
    jy60.o(i.jy60_init) refers to jy60.o(i.jy60_uart_recv_callback) for jy60_uart_recv_callback
    jy60.o(i.jy60_send_command) refers to usart.o(i.usart_send_data) for usart_send_data
    jy60.o(i.jy60_set_sleep_wake) refers to usart.o(i.usart_send_data) for usart_send_data
    jy60.o(i.jy60_set_sleep_wake) refers to jy60.o(.data) for .data
    jy60.o(i.jy60_set_sleep_wake) refers to jy60.o(i.jy60_send_command) for i.jy60_send_command
    jy60.o(i.jy60_uart_recv_callback) refers to memcpya.o(.text) for __aeabi_memcpy
    jy60.o(i.jy60_uart_recv_callback) refers to memseta.o(.text) for __aeabi_memclr4
    jy60.o(i.jy60_uart_recv_callback) refers to jy60.o(.bss) for .bss
    jy60.o(i.jy60_uart_recv_callback) refers to jy60.o(.data) for .data
    jy60.o(i.jy60_up_data) refers to jy60.o(.data) for .data
    jy60.o(i.jy60_up_data) refers to jy60.o(.bss) for .bss
    jyxx.o(i.jyxx_get_acceleration) refers to jy60.o(i.jy60_get_acceleration) for jy60_get_acceleration
    jyxx.o(i.jyxx_get_angle) refers to jy60.o(i.jy60_get_angle) for jy60_get_angle
    jyxx.o(i.jyxx_get_angle_speed) refers to jy60.o(i.jy60_get_angle_speed) for jy60_get_angle_speed
    jyxx.o(i.jyxx_get_temperature) refers to jy60.o(i.jy60_get_temperature) for jy60_get_temperature
    jyxx.o(i.jyxx_get_version) refers to jy60.o(i.jy60_get_version) for jy60_get_version
    jyxx.o(i.jyxx_get_voltage) refers to jy60.o(i.jy60_get_voltage) for jy60_get_voltage
    jyxx.o(i.jyxx_init) refers to jy60.o(i.jy60_init) for jy60_init
    jyxx.o(i.jyxx_oled_show) refers to jy60.o(i.jy60_get_acceleration) for jy60_get_acceleration
    jyxx.o(i.jyxx_oled_show) refers to f2d.o(.text) for __aeabi_f2d
    jyxx.o(i.jyxx_oled_show) refers to oled.o(i.OLED_Printf) for OLED_Printf
    jyxx.o(i.jyxx_oled_show) refers to jy60.o(i.jy60_get_angle_speed) for jy60_get_angle_speed
    jyxx.o(i.jyxx_oled_show) refers to jy60.o(i.jy60_get_angle) for jy60_get_angle
    jyxx.o(i.jyxx_set_sleep_wake) refers to jy60.o(i.jy60_set_sleep_wake) for jy60_set_sleep_wake
    jyxx.o(i.jyxx_up_data) refers to jy60.o(i.jy60_up_data) for jy60_up_data
    tb6612.o(i.tb6612_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    tb6612.o(i.tb6612_init) refers to tim.o(i.tim_pwm_pin_init) for tim_pwm_pin_init
    tb6612.o(i.tb6612_init) refers to tim.o(i.tim_pwm_set_ccr) for tim_pwm_set_ccr
    tb6612.o(i.tb6612_init) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    tb6612.o(i.tb6612_init) refers to tb6612.o(.constdata) for .constdata
    tb6612.o(i.tb6612_set_direction) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    tb6612.o(i.tb6612_set_direction) refers to tb6612.o(.constdata) for .constdata
    tb6612.o(i.tb6612_set_pwm_value) refers to tim.o(i.tim_pwm_set_ccr) for tim_pwm_set_ccr
    tb6612.o(i.tb6612_set_pwm_value) refers to tb6612.o(.constdata) for .constdata
    motor.o(i.motor_get_speed) refers to encoder.o(i.encoder_get_value) for encoder_get_value
    motor.o(i.motor_init) refers to tb6612.o(i.tb6612_init) for tb6612_init
    motor.o(i.motor_set_direction) refers to tb6612.o(i.tb6612_set_direction) for tb6612_set_direction
    motor.o(i.motor_set_pwm_value) refers to tb6612.o(i.tb6612_set_pwm_value) for tb6612_set_pwm_value
    motor.o(i.motor_set_speed) refers to tb6612.o(i.tb6612_set_direction) for tb6612_set_direction
    motor.o(i.motor_set_speed) refers to tb6612.o(i.tb6612_set_pwm_value) for tb6612_set_pwm_value
    motor.o(i.motor_set_speed) refers to motor.o(.data) for .data
    ultrasound.o(i.ultrasound_init) refers to tim.o(i.tim_cap_pin_init) for tim_cap_pin_init
    ultrasound.o(i.ultrasound_init) refers to gpio.o(i.gpio_pin_init) for gpio_pin_init
    ultrasound.o(i.ultrasound_init) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    ultrasound.o(i.ultrasound_init) refers to ultrasound.o(.bss) for .bss
    ultrasound.o(i.ultrasound_read) refers to ultrasound.o(.bss) for .bss
    ultrasound.o(i.ultrasound_read_distance) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    ultrasound.o(i.ultrasound_read_distance) refers to delay.o(i.delay_us) for delay_us
    ultrasound.o(i.ultrasound_read_distance) refers to ultrasound.o(.bss) for .bss
    ultrasound.o(i.ultrasound_start) refers to gpio.o(i.gpio_write_pin) for gpio_write_pin
    ultrasound.o(i.ultrasound_start) refers to delay.o(i.delay_us) for delay_us
    ultrasound.o(i.ultrasound_tim_ic_handler) refers to tim.o(i.tim_cap_get_value) for tim_cap_get_value
    ultrasound.o(i.ultrasound_tim_ic_handler) refers to ultrasound.o(.bss) for .bss
    ultrasound.o(i.ultrasound_tim_overflow_handler) refers to ultrasound.o(.bss) for .bss
    main.o(i.app_init) refers to app_state.o(i.app_state_task_start) for app_state_task_start
    main.o(i.app_init) refers to debug.o(i.debug_printf) for debug_printf
    main.o(i.app_init) refers to app_key.o(i.app_key_task_start) for app_key_task_start
    main.o(i.app_init) refers to app_ui.o(i.app_ui_task_start) for app_ui_task_start
    main.o(i.board_init) refers to led.o(i.led_init) for led_init
    main.o(i.board_init) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.board_init) refers to key.o(i.key_init) for key_init
    main.o(i.board_init) refers to encoder.o(i.encoder_init) for encoder_init
    main.o(i.board_init) refers to track.o(i.track_init) for track_init
    main.o(i.board_init) refers to jyxx.o(i.jyxx_init) for jyxx_init
    main.o(i.board_init) refers to motor.o(i.motor_init) for motor_init
    main.o(i.board_init) refers to ultrasound.o(i.ultrasound_init) for ultrasound_init
    main.o(i.main) refers to sys.o(i.sys_stm32_clock_init) for sys_stm32_clock_init
    main.o(i.main) refers to debug.o(i.debug_init) for debug_init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to sys.o(i.sys_check_rst) for sys_check_rst
    main.o(i.main) refers to malloc.o(i.my_mem_init) for my_mem_init
    main.o(i.main) refers to led.o(i.led_init) for led_init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to key.o(i.key_init) for key_init
    main.o(i.main) refers to encoder.o(i.encoder_init) for encoder_init
    main.o(i.main) refers to track.o(i.track_init) for track_init
    main.o(i.main) refers to jyxx.o(i.jyxx_init) for jyxx_init
    main.o(i.main) refers to motor.o(i.motor_init) for motor_init
    main.o(i.main) refers to ultrasound.o(i.ultrasound_init) for ultrasound_init
    main.o(i.main) refers to app_state.o(i.app_state_task_start) for app_state_task_start
    main.o(i.main) refers to debug.o(i.debug_printf) for debug_printf
    main.o(i.main) refers to app_key.o(i.app_key_task_start) for app_key_task_start
    main.o(i.main) refers to app_ui.o(i.app_ui_task_start) for app_ui_task_start
    main.o(i.main) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    main.o(i.main) refers to usmart_config.o(.data) for usmart_dev
    main.o(i.main) refers to main.o(i.app_init) for i.app_init
    main.o(i.sys_init) refers to sys.o(i.sys_stm32_clock_init) for sys_stm32_clock_init
    main.o(i.sys_init) refers to debug.o(i.debug_init) for debug_init
    main.o(i.sys_init) refers to delay.o(i.delay_init) for delay_init
    main.o(i.sys_init) refers to sys.o(i.sys_check_rst) for sys_check_rst
    main.o(i.sys_init) refers to malloc.o(i.my_mem_init) for my_mem_init
    main.o(i.sys_init) refers to usmart_config.o(.data) for usmart_dev
    app_key.o(i.app_key_callback_register) refers to app_key.o(.bss) for .bss
    app_key.o(i.app_key_callback_register) refers to app_key.o(.data) for .data
    app_key.o(i.app_key_callback_unregister) refers to app_key.o(.data) for .data
    app_key.o(i.app_key_callback_unregister) refers to app_key.o(.bss) for .bss
    app_key.o(i.app_key_task) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    app_key.o(i.app_key_task) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    app_key.o(i.app_key_task) refers to key.o(i.key_get_value) for key_get_value
    app_key.o(i.app_key_task) refers to debug.o(i.debug_message) for debug_message
    app_key.o(i.app_key_task) refers to sys.o(i.sys_soft_reset) for sys_soft_reset
    app_key.o(i.app_key_task) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    app_key.o(i.app_key_task) refers to app_key.o(.data) for .data
    app_key.o(i.app_key_task) refers to app_key.o(.bss) for .bss
    app_key.o(i.app_key_task) refers to app_key.o(.constdata) for .constdata
    app_key.o(i.app_key_task_start) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_key.o(i.app_key_task_start) refers to app_key.o(.data) for .data
    app_key.o(i.app_key_task_start) refers to app_key.o(i.app_key_task) for app_key_task
    app_key.o(i.app_key_task_stop) refers to app_key.o(.data) for .data
    app_state.o(i.app_state_task) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    app_state.o(i.app_state_task) refers to ultrasound.o(i.ultrasound_read_distance) for ultrasound_read_distance
    app_state.o(i.app_state_task) refers to debug.o(i.debug_printf) for debug_printf
    app_state.o(i.app_state_task) refers to led.o(i.led_toggle) for led_toggle
    app_state.o(i.app_state_task) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    app_state.o(i.app_state_task) refers to malloc.o(i.my_mem_perused) for my_mem_perused
    app_state.o(i.app_state_task) refers to heap_4.o(i.xPortGetFreeHeapSize) for xPortGetFreeHeapSize
    app_state.o(i.app_state_task) refers to tasks.o(i.vTaskListTasks) for vTaskListTasks
    app_state.o(i.app_state_task) refers to debug.o(i.my_printf) for my_printf
    app_state.o(i.app_state_task) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    app_state.o(i.app_state_task) refers to app_state.o(.data) for .data
    app_state.o(i.app_state_task) refers to app_state.o(.bss) for .bss
    app_state.o(i.app_state_task_start) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_state.o(i.app_state_task_start) refers to app_state.o(.data) for .data
    app_state.o(i.app_state_task_start) refers to app_state.o(i.app_state_task) for app_state_task
    app_state.o(i.app_state_task_stop) refers to app_state.o(.data) for .data
    app_ui.o(i.app_ui_task) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    app_ui.o(i.app_ui_task) refers to oled.o(i.OLED_Update) for OLED_Update
    app_ui.o(i.app_ui_task) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    app_ui.o(i.app_ui_task) refers to oled.o(i.OLED_Printf) for OLED_Printf
    app_ui.o(i.app_ui_task) refers to jyxx.o(i.jyxx_oled_show) for jyxx_oled_show
    app_ui.o(i.app_ui_task) refers to track.o(i.track_oled_show) for track_oled_show
    app_ui.o(i.app_ui_task) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    app_ui.o(i.app_ui_task) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    app_ui.o(i.app_ui_task) refers to app_ui.o(.data) for .data
    app_ui.o(i.app_ui_task_start) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_ui.o(i.app_ui_task_start) refers to app_ui.o(.data) for .data
    app_ui.o(i.app_ui_task_start) refers to app_ui.o(i.app_ui_task) for app_ui_task
    app_ui.o(i.app_ui_task_stop) refers to app_ui.o(.data) for .data
    app_motor.o(i.app_motor_key_handle) refers to app_motor.o(.data) for .data
    app_motor.o(i.app_motor_task) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    app_motor.o(i.app_motor_task) refers to pid.o(i.pid_init) for pid_init
    app_motor.o(i.app_motor_task) refers to pid.o(i.pid_set_target) for pid_set_target
    app_motor.o(i.app_motor_task) refers to jyxx.o(i.jyxx_up_data) for jyxx_up_data
    app_motor.o(i.app_motor_task) refers to track.o(i.track_read_sensors) for track_read_sensors
    app_motor.o(i.app_motor_task) refers to motor.o(i.motor_get_speed) for motor_get_speed
    app_motor.o(i.app_motor_task) refers to pid.o(i.pid_calculate_incremental) for pid_calculate_incremental
    app_motor.o(i.app_motor_task) refers to pid.o(i.pid_constrain) for pid_constrain
    app_motor.o(i.app_motor_task) refers to f2d.o(.text) for __aeabi_f2d
    app_motor.o(i.app_motor_task) refers to debug.o(i.my_printf) for my_printf
    app_motor.o(i.app_motor_task) refers to motor.o(i.motor_set_speed) for motor_set_speed
    app_motor.o(i.app_motor_task) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    app_motor.o(i.app_motor_task) refers to app_motor.o(.data) for .data
    app_motor.o(i.app_motor_task) refers to app_motor.o(.bss) for .bss
    app_motor.o(i.app_motor_task_start) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_motor.o(i.app_motor_task_start) refers to app_key.o(i.app_key_callback_register) for app_key_callback_register
    app_motor.o(i.app_motor_task_start) refers to app_motor.o(.data) for .data
    app_motor.o(i.app_motor_task_start) refers to app_motor.o(i.app_motor_task) for app_motor_task
    app_motor.o(i.app_motor_task_start) refers to app_motor.o(i.app_motor_key_handle) for app_motor_key_handle
    app_motor.o(i.app_motor_task_stop) refers to app_key.o(i.app_key_callback_unregister) for app_key_callback_unregister
    app_motor.o(i.app_motor_task_stop) refers to app_motor.o(.data) for .data
    app_motor.o(i.app_motor_task_stop) refers to app_motor.o(i.app_motor_key_handle) for app_motor_key_handle
    app_motor.o(i.motor_pid_init) refers to pid.o(i.pid_init) for pid_init
    app_motor.o(i.motor_pid_init) refers to pid.o(i.pid_set_target) for pid_set_target
    app_motor.o(i.motor_pid_init) refers to app_motor.o(.data) for .data
    app_motor.o(i.motor_pid_init) refers to app_motor.o(.bss) for .bss
    app_motor.o(i.pid_test) refers to pid.o(i.pid_init) for pid_init
    app_motor.o(i.pid_test) refers to app_motor.o(.data) for .data
    app_motor.o(i.pid_test) refers to app_motor.o(.bss) for .bss
    malloc.o(i.my_mem_init) refers to malloc.o(.data) for .data
    malloc.o(i.my_mem_init) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_malloc) refers to malloc.o(.data) for .data
    malloc.o(i.my_mem_malloc) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_perused) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.my_mem_perused) refers to malloc.o(.data) for .data
    malloc.o(i.myfree) refers to malloc.o(.data) for .data
    malloc.o(i.myfree) refers to malloc.o(.constdata) for .constdata
    malloc.o(i.mymalloc) refers to malloc.o(i.my_mem_malloc) for my_mem_malloc
    malloc.o(i.mymalloc) refers to malloc.o(.data) for .data
    malloc.o(i.myrealloc) refers to malloc.o(i.my_mem_malloc) for my_mem_malloc
    malloc.o(i.myrealloc) refers to malloc.o(.data) for .data
    malloc.o(i.myrealloc) refers to malloc.o(.constdata) for .constdata
    malloc.o(.data) refers to malloc.o(i.my_mem_init) for my_mem_init
    malloc.o(.data) refers to malloc.o(i.my_mem_perused) for my_mem_perused
    malloc.o(.data) refers to malloc.o(.bss) for mem1base
    malloc.o(.data) refers to malloc.o(.bss) for mem1mapbase
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart.o(i.usmart_cmd_rec) refers to usmart_str.o(i.usmart_get_fparam) for usmart_get_fparam
    usmart.o(i.usmart_cmd_rec) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_exe) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_exe) refers to debug.o(i.my_printf) for my_printf
    usmart.o(i.usmart_exe) refers to usmart_str.o(i.usmart_get_parmpos) for usmart_get_parmpos
    usmart.o(i.usmart_exe) refers to usmart_port.o(i.usmart_timx_reset_time) for usmart_timx_reset_time
    usmart.o(i.usmart_exe) refers to usmart_port.o(i.usmart_timx_get_time) for usmart_timx_get_time
    usmart.o(i.usmart_exe) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_init) refers to usmart_port.o(i.usmart_timx_init) for usmart_timx_init
    usmart.o(i.usmart_init) refers to usart.o(i.usart_iqr_callback_register) for usart_iqr_callback_register
    usmart.o(i.usmart_init) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_init) refers to usmart_port.o(i.usmart_recv_func) for usmart_recv_func
    usmart.o(i.usmart_scan) refers to usmart_port.o(i.usmart_get_input_string) for usmart_get_input_string
    usmart.o(i.usmart_scan) refers to usmart.o(i.usmart_sys_cmd_exe) for usmart_sys_cmd_exe
    usmart.o(i.usmart_scan) refers to debug.o(i.my_printf) for my_printf
    usmart.o(i.usmart_scan) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_cmdname) for usmart_get_cmdname
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_strcmp) for usmart_strcmp
    usmart.o(i.usmart_sys_cmd_exe) refers to debug.o(i.my_printf) for my_printf
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart.o(.data) for .data
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_config.o(.data) for usmart_dev
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_fname) for usmart_get_fname
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_get_aparm) for usmart_get_aparm
    usmart.o(i.usmart_sys_cmd_exe) refers to usmart_str.o(i.usmart_str2num) for usmart_str2num
    usmart.o(.data) refers to usmart.o(.conststring) for .conststring
    usmart_config.o(.data) refers to usmart.o(i.read_addr) for read_addr
    usmart_config.o(.data) refers to usmart_config.o(.conststring) for .conststring
    usmart_config.o(.data) refers to usmart.o(i.write_addr) for write_addr
    usmart_config.o(.data) refers to delay.o(i.delay_ms) for delay_ms
    usmart_config.o(.data) refers to delay.o(i.delay_us) for delay_us
    usmart_config.o(.data) refers to sys.o(i.sys_soft_reset) for sys_soft_reset
    usmart_config.o(.data) refers to motor.o(i.motor_set_speed) for motor_set_speed
    usmart_config.o(.data) refers to usmart_config.o(.data) for usmart_nametab
    usmart_config.o(.data) refers to usmart.o(i.usmart_init) for usmart_init
    usmart_config.o(.data) refers to usmart.o(i.usmart_cmd_rec) for usmart_cmd_rec
    usmart_config.o(.data) refers to usmart.o(i.usmart_exe) for usmart_exe
    usmart_config.o(.data) refers to usmart.o(i.usmart_scan) for usmart_scan
    usmart_port.o(i.TIM7_IRQHandler) refers to usmart_config.o(.data) for usmart_dev
    usmart_port.o(i.usmart_get_input_string) refers to usmart_port.o(.data) for .data
    usmart_port.o(i.usmart_get_input_string) refers to usmart_port.o(.bss) for .bss
    usmart_port.o(i.usmart_recv_func) refers to usmart_port.o(.data) for .data
    usmart_port.o(i.usmart_recv_func) refers to usmart_port.o(.bss) for .bss
    usmart_port.o(i.usmart_timx_get_time) refers to usmart_config.o(.data) for usmart_dev
    usmart_port.o(i.usmart_timx_init) refers to sys.o(i.sys_nvic_init) for sys_nvic_init
    usmart_port.o(i.usmart_timx_reset_time) refers to usmart_config.o(.data) for usmart_dev
    usmart_str.o(i.usmart_get_fparam) refers to usmart_str.o(i.__ARM_common_memclr4_10) for __ARM_common_memclr4_10
    usmart_str.o(i.usmart_get_fparam) refers to usmart_config.o(.data) for usmart_dev
    usmart_str.o(i.usmart_get_parmpos) refers to usmart_config.o(.data) for usmart_dev
    event_groups.o(i.vEventGroupClearBitsCallback) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.vEventGroupClearBitsCallback) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupSetBitsCallback) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupSetBitsCallback) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.pvPortCalloc) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    heap_4.o(i.pvPortCalloc) refers to memseta.o(.text) for __aeabi_memclr
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortHeapResetState) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvEnableVFP
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    port.o(i.xPortSysTickHandler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueCreateMutex) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueCreateMutex) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueCreateMutex) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueCreateMutex) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericCreate) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericCreate) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.eTaskGetState) refers to tasks.o(.data) for .data
    tasks.o(i.eTaskGetState) refers to tasks.o(.bss) for .bss
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvIdleTask) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvIdleTask) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to tasks.o(.data) for .data
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvListTasksWithinSingleList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvListTasksWithinSingleList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(.data) for .data
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(.bss) for .bss
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.bss) for .bss
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to timers.o(i.xTimerGetTimerDaemonTaskHandle) for xTimerGetTimerDaemonTaskHandle
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskEndScheduler) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskEndScheduler) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskEndScheduler) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskGetInfo) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskGetInfo) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskListTasks) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.vTaskListTasks) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.vTaskListTasks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskListTasks) refers to strcpy.o(.text) for strcpy
    tasks.o(i.vTaskListTasks) refers to strlen.o(.text) for strlen
    tasks.o(i.vTaskListTasks) refers to printfa.o(i.__0snprintf) for __2snprintf
    tasks.o(i.vTaskListTasks) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.vTaskListTasks) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskListTasks) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResetState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to memseta.o(.text) for __aeabi_memclr4
    tasks.o(i.xTaskCreate) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.xTaskCreate) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.xTaskCreate) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreate) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.xTaskCreate) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCreate) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetCurrentTaskHandleForCore) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    timers.o(i.prvProcessExpiredTimer) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessExpiredTimer) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(.data) for .data
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvSampleTimeNow) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for .data
    timers.o(i.prvTimerTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvTimerTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvTimerTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvTimerTask) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvTimerTask) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvTimerTask) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvTimerTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessExpiredTimer) for prvProcessExpiredTimer
    timers.o(i.prvTimerTask) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvTimerTask) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvTimerTask) refers to timers.o(.data) for .data
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerResetState) refers to timers.o(.data) for .data
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerCreate) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.xTimerCreate) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(i.xTimerCreate) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.xTimerCreate) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreate) refers to timers.o(.bss) for .bss
    timers.o(i.xTimerCreateTimerTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerCreateTimerTask) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.xTimerCreateTimerTask) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(i.xTimerCreateTimerTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.bss) for .bss
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommandFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommandFromISR) refers to timers.o(.data) for .data
    timers.o(i.xTimerGenericCommandFromTask) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommandFromTask) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommandFromTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for .data
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cjson.o(i.add_item_to_object) refers to strlen.o(.text) for strlen
    cjson.o(i.add_item_to_object) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_AddArrayToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddArrayToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddArrayToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddBoolToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddBoolToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddBoolToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddFalseToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddFalseToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddFalseToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddItemReferenceToArray) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddItemReferenceToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddItemReferenceToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddItemToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddItemToObjectCS) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddItemToObjectCS) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddNullToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddNullToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddNullToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddNumberToObject) refers to cjson.o(i.cJSON_CreateNumber) for cJSON_CreateNumber
    cjson.o(i.cJSON_AddNumberToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddNumberToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddNumberToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddObjectToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddObjectToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddObjectToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddRawToObject) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_AddRawToObject) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_AddRawToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddRawToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddRawToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddStringToObject) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_AddStringToObject) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_AddStringToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddStringToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddStringToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_AddTrueToObject) refers to cjson.o(i.add_item_to_object) for add_item_to_object
    cjson.o(i.cJSON_AddTrueToObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_AddTrueToObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_Compare) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    cjson.o(i.cJSON_Compare) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.cJSON_Compare) refers to dmul.o(.text) for __aeabi_dmul
    cjson.o(i.cJSON_Compare) refers to dadd.o(.text) for __aeabi_dsub
    cjson.o(i.cJSON_Compare) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.cJSON_Compare) refers to strcmp.o(.text) for strcmp
    cjson.o(i.cJSON_Compare) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_CreateArray) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateArrayReference) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateBool) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateDoubleArray) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.cJSON_CreateDoubleArray) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.cJSON_CreateDoubleArray) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateDoubleArray) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateFalse) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateFloatArray) refers to f2d.o(.text) for __aeabi_f2d
    cjson.o(i.cJSON_CreateFloatArray) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.cJSON_CreateFloatArray) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.cJSON_CreateFloatArray) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateFloatArray) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateIntArray) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.cJSON_CreateIntArray) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.cJSON_CreateIntArray) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.cJSON_CreateIntArray) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateIntArray) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateNull) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateNumber) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.cJSON_CreateNumber) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.cJSON_CreateNumber) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.cJSON_CreateNumber) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateObject) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateObjectReference) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateRaw) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_CreateRaw) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_CreateRaw) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateRaw) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateString) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_CreateString) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_CreateString) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateString) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateStringArray) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_CreateStringArray) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_CreateStringArray) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateStringReference) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_CreateTrue) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_Delete) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_DeleteItemFromArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DeleteItemFromObject) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_DeleteItemFromObject) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DeleteItemFromObjectCaseSensitive) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_DeleteItemFromObjectCaseSensitive) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_DetachItemFromObject) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_DetachItemFromObjectCaseSensitive) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_Duplicate) refers to cjson.o(i.cJSON_Duplicate_rec) for cJSON_Duplicate_rec
    cjson.o(i.cJSON_Duplicate_rec) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_Duplicate_rec) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_Duplicate_rec) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_Duplicate_rec) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_GetErrorPtr) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_GetObjectItem) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_GetObjectItemCaseSensitive) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_HasObjectItem) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.cJSON_InitHooks) refers to mallocr.o(i.realloc) for realloc
    cjson.o(i.cJSON_InitHooks) refers to mallocr.o(i.__malloc$realloc) for malloc
    cjson.o(i.cJSON_InitHooks) refers to mallocr.o(i.__free$realloc) for free
    cjson.o(i.cJSON_InitHooks) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_Parse) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_Parse) refers to cjson.o(i.cJSON_ParseWithLengthOpts) for cJSON_ParseWithLengthOpts
    cjson.o(i.cJSON_ParseWithLength) refers to cjson.o(i.cJSON_ParseWithLengthOpts) for cJSON_ParseWithLengthOpts
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to strncmp.o(.text) for strncmp
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(i.buffer_skip_whitespace) for buffer_skip_whitespace
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(i.parse_value) for parse_value
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_ParseWithLengthOpts) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_ParseWithOpts) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_ParseWithOpts) refers to cjson.o(i.cJSON_ParseWithLengthOpts) for cJSON_ParseWithLengthOpts
    cjson.o(i.cJSON_Print) refers to cjson.o(i.print) for print
    cjson.o(i.cJSON_Print) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_PrintBuffered) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_PrintBuffered) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_PrintPreallocated) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.cJSON_PrintPreallocated) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_PrintUnformatted) refers to cjson.o(i.print) for print
    cjson.o(i.cJSON_PrintUnformatted) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_ReplaceItemInArray) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_ReplaceItemInObject) refers to cjson.o(i.replace_item_in_object) for replace_item_in_object
    cjson.o(i.cJSON_ReplaceItemInObjectCaseSensitive) refers to cjson.o(i.replace_item_in_object) for replace_item_in_object
    cjson.o(i.cJSON_ReplaceItemViaPointer) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.cJSON_SetNumberHelper) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.cJSON_SetNumberHelper) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.cJSON_SetNumberHelper) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.cJSON_SetValuestring) refers to strlen.o(.text) for strlen
    cjson.o(i.cJSON_SetValuestring) refers to strcpy.o(.text) for strcpy
    cjson.o(i.cJSON_SetValuestring) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.cJSON_SetValuestring) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_Version) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.cJSON_Version) refers to cjson.o(.bss) for .bss
    cjson.o(i.cJSON_free) refers to cjson.o(.data) for .data
    cjson.o(i.cJSON_malloc) refers to cjson.o(.data) for .data
    cjson.o(i.ensure) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.get_object_item) refers to strcmp.o(.text) for strcmp
    cjson.o(i.get_object_item) refers to tolower.o(.text) for tolower
    cjson.o(i.parse_value) refers to strncmp.o(.text) for strncmp
    cjson.o(i.parse_value) refers to cjson.o(i.parse_string) for parse_string
    cjson.o(i.parse_value) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.parse_value) refers to strtod.o(i.__hardfp_strtod) for __hardfp_strtod
    cjson.o(i.parse_value) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.parse_value) refers to cdcmple.o(.text) for __aeabi_cdcmple
    cjson.o(i.parse_value) refers to dfixi.o(.text) for __aeabi_d2iz
    cjson.o(i.parse_value) refers to cjson.o(i.buffer_skip_whitespace) for buffer_skip_whitespace
    cjson.o(i.parse_value) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.print) refers to cjson.o(i.print_value) for print_value
    cjson.o(i.print) refers to strlen.o(.text) for strlen
    cjson.o(i.print) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_string_ptr) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_string_ptr) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_string_ptr) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.print_string_ptr) refers to cjson.o(i.print_value) for i.print_value
    cjson.o(i.print_value) refers to scanf_fp.o(.text) for _scanf_real
    cjson.o(i.print_value) refers to cjson.o(i.ensure) for ensure
    cjson.o(i.print_value) refers to cjson.o(i.__ARM_common_memcpy4_5) for __ARM_common_memcpy4_5
    cjson.o(i.print_value) refers to printfa.o(i.__0sprintf) for __2sprintf
    cjson.o(i.print_value) refers to dflti.o(.text) for __aeabi_i2d
    cjson.o(i.print_value) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    cjson.o(i.print_value) refers to __0sscanf.o(.text) for __0sscanf
    cjson.o(i.print_value) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    cjson.o(i.print_value) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    cjson.o(i.print_value) refers to dmul.o(.text) for __aeabi_dmul
    cjson.o(i.print_value) refers to dadd.o(.text) for __aeabi_dsub
    cjson.o(i.print_value) refers to strlen.o(.text) for strlen
    cjson.o(i.print_value) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.print_value) refers to cjson.o(i.print_string_ptr) for print_string_ptr
    cjson.o(i.replace_item_in_object) refers to strlen.o(.text) for strlen
    cjson.o(i.replace_item_in_object) refers to memcpya.o(.text) for __aeabi_memcpy
    cjson.o(i.replace_item_in_object) refers to cjson.o(i.get_object_item) for get_object_item
    cjson.o(i.replace_item_in_object) refers to cjson.o(i.cJSON_Delete) for cJSON_Delete
    cjson.o(i.replace_item_in_object) refers to cjson.o(.data) for .data
    cjson.o(.data) refers to mallocr.o(i.__malloc$realloc) for malloc
    cjson.o(.data) refers to mallocr.o(i.__free$realloc) for free
    cjson.o(.data) refers to mallocr.o(i.realloc) for realloc
    lwrb.o(i.lwrb_peek) refers to memcpya.o(.text) for __aeabi_memcpy
    lwrb.o(i.lwrb_read) refers to lwrb.o(i.lwrb_read_ex) for lwrb_read_ex
    lwrb.o(i.lwrb_read_ex) refers to memcpya.o(.text) for __aeabi_memcpy
    lwrb.o(i.lwrb_write) refers to lwrb.o(i.lwrb_write_ex) for lwrb_write_ex
    lwrb.o(i.lwrb_write_ex) refers to memcpya.o(.text) for __aeabi_memcpy
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.__hardfp_round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.__hardfp_round) refers to drnd.o(.text) for _drnd
    round.o(i.__hardfp_round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.__hardfp_round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.__hardfp_round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    round.o(i.round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.round) refers to drnd.o(.text) for _drnd
    round.o(i.round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    strtod.o(i.__hardfp_strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.__hardfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.__softfp_strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.__softfp_strtod) refers to strtod.o(.text) for __strtod_int
    strtod.o(i.strtod) refers (Special) to iusefp.o(.text) for __I$use$fp
    strtod.o(i.strtod) refers to strtod.o(.text) for __strtod_int
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for free
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for malloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_c.o(.text) for isspace
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_c.o(.text) for isspace
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    drnd.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to depilogue.o(.text) for _double_round
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (1024 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing gpio.o(i.gpio_ex_callback_deregister), (20 bytes).
    Removing gpio.o(i.gpio_ex_callback_register), (72 bytes).
    Removing gpio.o(i.gpio_nvic_ex_config), (296 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(i.sys_intx_disable), (4 bytes).
    Removing sys.o(i.sys_intx_enable), (4 bytes).
    Removing sys.o(i.sys_msr_msp), (6 bytes).
    Removing sys.o(i.sys_nvic_set_vector_table), (16 bytes).
    Removing sys.o(i.sys_standby), (60 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing usart.o(i.usart_iqr_callback_deregister), (16 bytes).
    Removing usart.o(i.usart_send_byte), (84 bytes).
    Removing usart.o(.data), (2 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.i2c_ack), (160 bytes).
    Removing i2c.o(i.i2c_delay), (26 bytes).
    Removing i2c.o(i.i2c_nack), (128 bytes).
    Removing i2c.o(i.i2c_read_byte), (340 bytes).
    Removing i2c.o(i.i2c_read_data), (320 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing dma.o(i.DMA2_Channel3_IRQHandler), (2 bytes).
    Removing dma.o(i.dma_basic_config), (72 bytes).
    Removing dma.o(i.dma_enable), (28 bytes).
    Removing dma.o(.data), (4 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.spi_pin_init), (304 bytes).
    Removing spi.o(i.spi_read_write_byte), (60 bytes).
    Removing spi.o(i.spi_set_speed), (72 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.adc_get_value), (56 bytes).
    Removing adc.o(i.adc_pin_init), (644 bytes).
    Removing adc.o(.bss), (48 bytes).
    Removing adc.o(.constdata), (301 bytes).
    Removing adc.o(.data), (1 bytes).
    Removing adc.o(.data), (1 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.btimx_int_init), (60 bytes).
    Removing tim.o(i.tim_cnt_get_value), (168 bytes).
    Removing tim.o(i.tim_int_callback_deregister), (124 bytes).
    Removing wdg.o(.rev16_text), (4 bytes).
    Removing wdg.o(.revsh_text), (4 bytes).
    Removing wdg.o(.rrx_text), (6 bytes).
    Removing wdg.o(i.iwdg_feed), (16 bytes).
    Removing wdg.o(i.iwdg_init), (28 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing led.o(i.led_set), (40 bytes).
    Removing debug.o(.rev16_text), (4 bytes).
    Removing debug.o(.revsh_text), (4 bytes).
    Removing debug.o(.rrx_text), (6 bytes).
    Removing debug.o(i.debug_send_data), (10 bytes).
    Removing debug.o(i.debug_warning), (124 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Clear), (32 bytes).
    Removing oled.o(i.OLED_ClearArea), (120 bytes).
    Removing oled.o(i.OLED_DrawArc), (632 bytes).
    Removing oled.o(i.OLED_DrawCircle), (364 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (800 bytes).
    Removing oled.o(i.OLED_DrawLine), (296 bytes).
    Removing oled.o(i.OLED_DrawPoint), (60 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (140 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (330 bytes).
    Removing oled.o(i.OLED_GPIO_Init), (30 bytes).
    Removing oled.o(i.OLED_GetPoint), (60 bytes).
    Removing oled.o(i.OLED_IsInAngle), (136 bytes).
    Removing oled.o(i.OLED_Pow), (34 bytes).
    Removing oled.o(i.OLED_Reverse), (52 bytes).
    Removing oled.o(i.OLED_ReverseArea), (120 bytes).
    Removing oled.o(i.OLED_SetCursor), (80 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (104 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (436 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (128 bytes).
    Removing oled.o(i.OLED_ShowNum), (120 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (144 bytes).
    Removing oled.o(i.OLED_UpdateArea), (180 bytes).
    Removing oled.o(i.OLED_WriteCommand), (22 bytes).
    Removing oled.o(i.OLED_WriteData), (18 bytes).
    Removing oled.o(i.OLED_pnpoly), (122 bytes).
    Removing oled_data.o(.constdata), (32 bytes).
    Removing adc_key.o(.rev16_text), (4 bytes).
    Removing adc_key.o(.revsh_text), (4 bytes).
    Removing adc_key.o(.rrx_text), (6 bytes).
    Removing gpio_key.o(.rev16_text), (4 bytes).
    Removing gpio_key.o(.revsh_text), (4 bytes).
    Removing gpio_key.o(.rrx_text), (6 bytes).
    Removing gpio_key.o(i.gpio_key_read), (48 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing encoder.o(.rev16_text), (4 bytes).
    Removing encoder.o(.revsh_text), (4 bytes).
    Removing encoder.o(.rrx_text), (6 bytes).
    Removing encoder.o(i.encoder_get_value), (32 bytes).
    Removing track.o(.rev16_text), (4 bytes).
    Removing track.o(.revsh_text), (4 bytes).
    Removing track.o(.rrx_text), (6 bytes).
    Removing track.o(i.track_get_line_position_error), (156 bytes).
    Removing track.o(i.track_get_sensor_data), (12 bytes).
    Removing track.o(i.track_read_sensors), (40 bytes).
    Removing track.o(.constdata), (32 bytes).
    Removing jy60.o(.rev16_text), (4 bytes).
    Removing jy60.o(.revsh_text), (4 bytes).
    Removing jy60.o(.rrx_text), (6 bytes).
    Removing jy60.o(i.jy60_get_temperature), (12 bytes).
    Removing jy60.o(i.jy60_get_version), (12 bytes).
    Removing jy60.o(i.jy60_get_voltage), (12 bytes).
    Removing jy60.o(i.jy60_set_sleep_wake), (96 bytes).
    Removing jy60.o(i.jy60_up_data), (568 bytes).
    Removing jyxx.o(.rev16_text), (4 bytes).
    Removing jyxx.o(.revsh_text), (4 bytes).
    Removing jyxx.o(.rrx_text), (6 bytes).
    Removing jyxx.o(i.jyxx_get_acceleration), (4 bytes).
    Removing jyxx.o(i.jyxx_get_angle), (4 bytes).
    Removing jyxx.o(i.jyxx_get_angle_speed), (4 bytes).
    Removing jyxx.o(i.jyxx_get_temperature), (4 bytes).
    Removing jyxx.o(i.jyxx_get_version), (4 bytes).
    Removing jyxx.o(i.jyxx_get_voltage), (4 bytes).
    Removing jyxx.o(i.jyxx_set_sleep_wake), (4 bytes).
    Removing jyxx.o(i.jyxx_up_data), (4 bytes).
    Removing tb6612.o(.rev16_text), (4 bytes).
    Removing tb6612.o(.revsh_text), (4 bytes).
    Removing tb6612.o(.rrx_text), (6 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing motor.o(.rrx_text), (6 bytes).
    Removing motor.o(i.motor_get_speed), (68 bytes).
    Removing motor.o(i.motor_set_direction), (4 bytes).
    Removing motor.o(i.motor_set_pwm_value), (4 bytes).
    Removing ultrasound.o(.rev16_text), (4 bytes).
    Removing ultrasound.o(.revsh_text), (4 bytes).
    Removing ultrasound.o(.rrx_text), (6 bytes).
    Removing ultrasound.o(i.ultrasound_read), (40 bytes).
    Removing ultrasound.o(i.ultrasound_start), (28 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.board_init), (38 bytes).
    Removing main.o(i.sys_init), (84 bytes).
    Removing app_key.o(.rev16_text), (4 bytes).
    Removing app_key.o(.revsh_text), (4 bytes).
    Removing app_key.o(.rrx_text), (6 bytes).
    Removing app_key.o(i.app_key_callback_register), (24 bytes).
    Removing app_key.o(i.app_key_callback_unregister), (76 bytes).
    Removing app_key.o(i.app_key_task_stop), (12 bytes).
    Removing app_state.o(.rev16_text), (4 bytes).
    Removing app_state.o(.revsh_text), (4 bytes).
    Removing app_state.o(.rrx_text), (6 bytes).
    Removing app_state.o(i.app_state_task_stop), (12 bytes).
    Removing app_ui.o(.rev16_text), (4 bytes).
    Removing app_ui.o(.revsh_text), (4 bytes).
    Removing app_ui.o(.rrx_text), (6 bytes).
    Removing app_ui.o(i.app_ui_task_stop), (12 bytes).
    Removing app_motor.o(.rev16_text), (4 bytes).
    Removing app_motor.o(.revsh_text), (4 bytes).
    Removing app_motor.o(.rrx_text), (6 bytes).
    Removing app_motor.o(i.app_motor_key_handle), (40 bytes).
    Removing app_motor.o(i.app_motor_task), (556 bytes).
    Removing app_motor.o(i.app_motor_task_start), (88 bytes).
    Removing app_motor.o(i.app_motor_task_stop), (20 bytes).
    Removing app_motor.o(i.motor_pid_init), (128 bytes).
    Removing app_motor.o(i.pid_test), (84 bytes).
    Removing app_motor.o(.bss), (120 bytes).
    Removing app_motor.o(.data), (56 bytes).
    Removing malloc.o(.rev16_text), (4 bytes).
    Removing malloc.o(.revsh_text), (4 bytes).
    Removing malloc.o(.rrx_text), (6 bytes).
    Removing malloc.o(i.my_mem_copy), (48 bytes).
    Removing malloc.o(i.my_mem_malloc), (196 bytes).
    Removing malloc.o(i.my_mem_set), (36 bytes).
    Removing malloc.o(i.myfree), (128 bytes).
    Removing malloc.o(i.mymalloc), (36 bytes).
    Removing malloc.o(i.myrealloc), (196 bytes).
    Removing usmart.o(.rev16_text), (4 bytes).
    Removing usmart.o(.revsh_text), (4 bytes).
    Removing usmart.o(.rrx_text), (6 bytes).
    Removing usmart_config.o(.rev16_text), (4 bytes).
    Removing usmart_config.o(.revsh_text), (4 bytes).
    Removing usmart_config.o(.rrx_text), (6 bytes).
    Removing usmart_port.o(.rev16_text), (4 bytes).
    Removing usmart_port.o(.revsh_text), (4 bytes).
    Removing usmart_port.o(.rrx_text), (6 bytes).
    Removing usmart_str.o(.rev16_text), (4 bytes).
    Removing usmart_str.o(.revsh_text), (4 bytes).
    Removing usmart_str.o(.rrx_text), (6 bytes).
    Removing usmart_str.o(i.usmart_pow), (40 bytes).
    Removing usmart_str.o(i.usmart_search_nextc), (20 bytes).
    Removing usmart_str.o(i.usmart_strcopy), (18 bytes).
    Removing usmart_str.o(i.usmart_strlen), (20 bytes).
    Removing event_groups.o(i.uxEventGroupGetNumber), (8 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (24 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (46 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (106 bytes).
    Removing event_groups.o(i.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (26 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (26 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (26 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (108 bytes).
    Removing event_groups.o(i.xEventGroupSync), (236 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (188 bytes).
    Removing heap_4.o(i.pvPortCalloc), (38 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (120 bytes).
    Removing heap_4.o(i.vPortHeapResetState), (20 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (2 bytes).
    Removing queue.o(i.ucQueueGetQueueType), (6 bytes).
    Removing queue.o(i.uxQueueGetQueueItemSize), (4 bytes).
    Removing queue.o(i.uxQueueGetQueueLength), (4 bytes).
    Removing queue.o(i.uxQueueGetQueueNumber), (4 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (18 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (4 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (22 bytes).
    Removing queue.o(i.vQueueDelete), (4 bytes).
    Removing queue.o(i.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(i.xQueueCreateMutex), (148 bytes).
    Removing queue.o(i.xQueueGenericReset), (148 bytes).
    Removing queue.o(i.xQueueGenericSend), (368 bytes).
    Removing queue.o(i.xQueueGenericSendFromISR), (214 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (102 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (12 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (14 bytes).
    Removing queue.o(i.xQueuePeek), (276 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (64 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (134 bytes).
    Removing queue.o(i.xQueueSemaphoreTake), (328 bytes).
    Removing tasks.o(i.eTaskGetState), (88 bytes).
    Removing tasks.o(i.pcTaskGetName), (16 bytes).
    Removing tasks.o(i.pvTaskIncrementMutexHeldCount), (24 bytes).
    Removing tasks.o(i.ulTaskGenericNotifyTake), (156 bytes).
    Removing tasks.o(i.ulTaskGenericNotifyValueClear), (44 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskGetSystemState), (160 bytes).
    Removing tasks.o(i.uxTaskGetTaskNumber), (8 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskDelay), (56 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (300 bytes).
    Removing tasks.o(i.vTaskGenericNotifyGiveFromISR), (284 bytes).
    Removing tasks.o(i.vTaskGetInfo), (208 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (88 bytes).
    Removing tasks.o(i.vTaskPriorityDisinheritAfterTimeout), (168 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (188 bytes).
    Removing tasks.o(i.vTaskResetState), (40 bytes).
    Removing tasks.o(i.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (32 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (40 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (256 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (316 bytes).
    Removing tasks.o(i.xTaskGenericNotifyStateClear), (52 bytes).
    Removing tasks.o(i.xTaskGenericNotifyWait), (192 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandleForCore), (20 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (12 bytes).
    Removing tasks.o(i.xTaskPriorityDisinherit), (144 bytes).
    Removing tasks.o(i.xTaskPriorityInherit), (184 bytes).
    Removing timers.o(i.pcTimerGetName), (4 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (18 bytes).
    Removing timers.o(i.uxTimerGetReloadMode), (30 bytes).
    Removing timers.o(i.uxTimerGetTimerNumber), (4 bytes).
    Removing timers.o(i.vTimerResetState), (16 bytes).
    Removing timers.o(i.vTimerSetReloadMode), (38 bytes).
    Removing timers.o(i.vTimerSetTimerID), (20 bytes).
    Removing timers.o(i.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(i.xTimerCreate), (128 bytes).
    Removing timers.o(i.xTimerGenericCommandFromISR), (64 bytes).
    Removing timers.o(i.xTimerGenericCommandFromTask), (84 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (4 bytes).
    Removing timers.o(i.xTimerGetPeriod), (4 bytes).
    Removing timers.o(i.xTimerGetReloadMode), (30 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (12 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (30 bytes).
    Removing pid.o(i.pid_app_limit_integral), (40 bytes).
    Removing pid.o(i.pid_calculate_incremental), (156 bytes).
    Removing pid.o(i.pid_calculate_positional), (136 bytes).
    Removing pid.o(i.pid_constrain), (32 bytes).
    Removing pid.o(i.pid_init), (48 bytes).
    Removing pid.o(i.pid_reset), (40 bytes).
    Removing pid.o(i.pid_set_limit), (6 bytes).
    Removing pid.o(i.pid_set_params), (14 bytes).
    Removing pid.o(i.pid_set_target), (44 bytes).
    Removing cjson.o(i.add_item_to_object), (148 bytes).
    Removing cjson.o(i.buffer_skip_whitespace), (54 bytes).
    Removing cjson.o(i.cJSON_AddArrayToObject), (84 bytes).
    Removing cjson.o(i.cJSON_AddBoolToObject), (92 bytes).
    Removing cjson.o(i.cJSON_AddFalseToObject), (84 bytes).
    Removing cjson.o(i.cJSON_AddItemReferenceToArray), (132 bytes).
    Removing cjson.o(i.cJSON_AddItemReferenceToObject), (116 bytes).
    Removing cjson.o(i.cJSON_AddItemToArray), (46 bytes).
    Removing cjson.o(i.cJSON_AddItemToObject), (20 bytes).
    Removing cjson.o(i.cJSON_AddItemToObjectCS), (20 bytes).
    Removing cjson.o(i.cJSON_AddNullToObject), (84 bytes).
    Removing cjson.o(i.cJSON_AddNumberToObject), (48 bytes).
    Removing cjson.o(i.cJSON_AddObjectToObject), (84 bytes).
    Removing cjson.o(i.cJSON_AddRawToObject), (140 bytes).
    Removing cjson.o(i.cJSON_AddStringToObject), (140 bytes).
    Removing cjson.o(i.cJSON_AddTrueToObject), (84 bytes).
    Removing cjson.o(i.cJSON_Compare), (400 bytes).
    Removing cjson.o(i.cJSON_CreateArray), (48 bytes).
    Removing cjson.o(i.cJSON_CreateArrayReference), (52 bytes).
    Removing cjson.o(i.cJSON_CreateBool), (56 bytes).
    Removing cjson.o(i.cJSON_CreateDoubleArray), (312 bytes).
    Removing cjson.o(i.cJSON_CreateFalse), (48 bytes).
    Removing cjson.o(i.cJSON_CreateFloatArray), (312 bytes).
    Removing cjson.o(i.cJSON_CreateIntArray), (312 bytes).
    Removing cjson.o(i.cJSON_CreateNull), (48 bytes).
    Removing cjson.o(i.cJSON_CreateNumber), (152 bytes).
    Removing cjson.o(i.cJSON_CreateObject), (48 bytes).
    Removing cjson.o(i.cJSON_CreateObjectReference), (52 bytes).
    Removing cjson.o(i.cJSON_CreateRaw), (112 bytes).
    Removing cjson.o(i.cJSON_CreateString), (112 bytes).
    Removing cjson.o(i.cJSON_CreateStringArray), (232 bytes).
    Removing cjson.o(i.cJSON_CreateStringReference), (52 bytes).
    Removing cjson.o(i.cJSON_CreateTrue), (48 bytes).
    Removing cjson.o(i.cJSON_Delete), (88 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromArray), (108 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromObject), (88 bytes).
    Removing cjson.o(i.cJSON_DeleteItemFromObjectCaseSensitive), (88 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromArray), (106 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromObject), (82 bytes).
    Removing cjson.o(i.cJSON_DetachItemFromObjectCaseSensitive), (82 bytes).
    Removing cjson.o(i.cJSON_DetachItemViaPointer), (74 bytes).
    Removing cjson.o(i.cJSON_Duplicate), (8 bytes).
    Removing cjson.o(i.cJSON_Duplicate_rec), (288 bytes).
    Removing cjson.o(i.cJSON_GetArrayItem), (30 bytes).
    Removing cjson.o(i.cJSON_GetArraySize), (28 bytes).
    Removing cjson.o(i.cJSON_GetErrorPtr), (16 bytes).
    Removing cjson.o(i.cJSON_GetNumberValue), (32 bytes).
    Removing cjson.o(i.cJSON_GetObjectItem), (6 bytes).
    Removing cjson.o(i.cJSON_GetObjectItemCaseSensitive), (6 bytes).
    Removing cjson.o(i.cJSON_GetStringValue), (16 bytes).
    Removing cjson.o(i.cJSON_HasObjectItem), (16 bytes).
    Removing cjson.o(i.cJSON_InitHooks), (84 bytes).
    Removing cjson.o(i.cJSON_InsertItemInArray), (124 bytes).
    Removing cjson.o(i.cJSON_IsArray), (18 bytes).
    Removing cjson.o(i.cJSON_IsBool), (14 bytes).
    Removing cjson.o(i.cJSON_IsFalse), (16 bytes).
    Removing cjson.o(i.cJSON_IsInvalid), (20 bytes).
    Removing cjson.o(i.cJSON_IsNull), (18 bytes).
    Removing cjson.o(i.cJSON_IsNumber), (18 bytes).
    Removing cjson.o(i.cJSON_IsObject), (18 bytes).
    Removing cjson.o(i.cJSON_IsRaw), (18 bytes).
    Removing cjson.o(i.cJSON_IsString), (18 bytes).
    Removing cjson.o(i.cJSON_IsTrue), (18 bytes).
    Removing cjson.o(i.cJSON_Minify), (192 bytes).
    Removing cjson.o(i.cJSON_Parse), (34 bytes).
    Removing cjson.o(i.cJSON_ParseWithLength), (8 bytes).
    Removing cjson.o(i.cJSON_ParseWithLengthOpts), (240 bytes).
    Removing cjson.o(i.cJSON_ParseWithOpts), (34 bytes).
    Removing cjson.o(i.cJSON_Print), (12 bytes).
    Removing cjson.o(i.cJSON_PrintBuffered), (128 bytes).
    Removing cjson.o(i.cJSON_PrintPreallocated), (96 bytes).
    Removing cjson.o(i.cJSON_PrintUnformatted), (12 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInArray), (132 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInObject), (6 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemInObjectCaseSensitive), (6 bytes).
    Removing cjson.o(i.cJSON_ReplaceItemViaPointer), (104 bytes).
    Removing cjson.o(i.cJSON_SetNumberHelper), (112 bytes).
    Removing cjson.o(i.cJSON_SetValuestring), (144 bytes).
    Removing cjson.o(i.cJSON_Version), (40 bytes).
    Removing cjson.o(i.cJSON_free), (12 bytes).
    Removing cjson.o(i.cJSON_malloc), (12 bytes).
    Removing cjson.o(i.ensure), (156 bytes).
    Removing cjson.o(i.get_object_item), (144 bytes).
    Removing cjson.o(i.parse_string), (654 bytes).
    Removing cjson.o(i.parse_value), (1114 bytes).
    Removing cjson.o(i.print), (186 bytes).
    Removing cjson.o(i.print_string_ptr), (312 bytes).
    Removing cjson.o(i.print_value), (1560 bytes).
    Removing cjson.o(i.replace_item_in_object), (204 bytes).
    Removing cjson.o(.bss), (15 bytes).
    Removing cjson.o(.data), (20 bytes).
    Removing lwrb.o(i.lwrb_advance), (84 bytes).
    Removing lwrb.o(i.lwrb_find), (190 bytes).
    Removing lwrb.o(i.lwrb_free), (22 bytes).
    Removing lwrb.o(i.lwrb_get_arg), (8 bytes).
    Removing lwrb.o(i.lwrb_get_free), (36 bytes).
    Removing lwrb.o(i.lwrb_get_full), (34 bytes).
    Removing lwrb.o(i.lwrb_get_linear_block_read_address), (26 bytes).
    Removing lwrb.o(i.lwrb_get_linear_block_read_length), (38 bytes).
    Removing lwrb.o(i.lwrb_get_linear_block_write_address), (26 bytes).
    Removing lwrb.o(i.lwrb_get_linear_block_write_length), (42 bytes).
    Removing lwrb.o(i.lwrb_init), (32 bytes).
    Removing lwrb.o(i.lwrb_is_ready), (24 bytes).
    Removing lwrb.o(i.lwrb_peek), (126 bytes).
    Removing lwrb.o(i.lwrb_read), (26 bytes).
    Removing lwrb.o(i.lwrb_read_ex), (156 bytes).
    Removing lwrb.o(i.lwrb_reset), (42 bytes).
    Removing lwrb.o(i.lwrb_set_arg), (20 bytes).
    Removing lwrb.o(i.lwrb_set_evt_fn), (20 bytes).
    Removing lwrb.o(i.lwrb_skip), (78 bytes).
    Removing lwrb.o(i.lwrb_write), (26 bytes).
    Removing lwrb.o(i.lwrb_write_ex), (160 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dfixui.o(.text), (50 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing fepilogue.o(.text), (110 bytes).
    Removing drnd.o(.text), (136 bytes).
    Removing dfltul.o(.text), (24 bytes).
    Removing cjson.o(i.__ARM_common_memcpy4_5), (10 bytes).

427 unused section(s) (total 32900 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/tolower.c         0x00000000   Number         0  tolower.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  drnd.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ../mathlib/strtod.c                      0x00000000   Number         0  strtod.o ABSOLUTE
    ..\App\app_key.c                         0x00000000   Number         0  app_key.o ABSOLUTE
    ..\App\app_motor.c                       0x00000000   Number         0  app_motor.o ABSOLUTE
    ..\App\app_state.c                       0x00000000   Number         0  app_state.o ABSOLUTE
    ..\App\app_ui.c                          0x00000000   Number         0  app_ui.o ABSOLUTE
    ..\App\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\Drivers\BSP\DEBUG\debug.c             0x00000000   Number         0  debug.o ABSOLUTE
    ..\Drivers\BSP\ENCODER\encoder.c         0x00000000   Number         0  encoder.o ABSOLUTE
    ..\Drivers\BSP\JYxx\jy60.c               0x00000000   Number         0  jy60.o ABSOLUTE
    ..\Drivers\BSP\JYxx\jyxx.c               0x00000000   Number         0  jyxx.o ABSOLUTE
    ..\Drivers\BSP\KEY\adc_key.c             0x00000000   Number         0  adc_key.o ABSOLUTE
    ..\Drivers\BSP\KEY\gpio_key.c            0x00000000   Number         0  gpio_key.o ABSOLUTE
    ..\Drivers\BSP\KEY\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\Drivers\BSP\LED\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\Drivers\BSP\MOTOR\motor.c             0x00000000   Number         0  motor.o ABSOLUTE
    ..\Drivers\BSP\OLED\OLED.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\Drivers\BSP\OLED\OLED_Data.c          0x00000000   Number         0  oled_data.o ABSOLUTE
    ..\Drivers\BSP\TB6612\tb6612.c           0x00000000   Number         0  tb6612.o ABSOLUTE
    ..\Drivers\BSP\TRACK\track.c             0x00000000   Number         0  track.o ABSOLUTE
    ..\Drivers\BSP\UlTraSound\ultrasound.c   0x00000000   Number         0  ultrasound.o ABSOLUTE
    ..\Drivers\CMSIS\Device\ST\STM32F4xx\Source\Templates\arm\startup_stm32f407xx.s 0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\Drivers\PORT\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\Drivers\PORT\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\Drivers\PORT\dma.c                    0x00000000   Number         0  dma.o ABSOLUTE
    ..\Drivers\PORT\gpio.c                   0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Drivers\PORT\i2c.c                    0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Drivers\PORT\spi.c                    0x00000000   Number         0  spi.o ABSOLUTE
    ..\Drivers\PORT\sys.c                    0x00000000   Number         0  sys.o ABSOLUTE
    ..\Drivers\PORT\tim.c                    0x00000000   Number         0  tim.o ABSOLUTE
    ..\Drivers\PORT\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\PORT\wdg.c                    0x00000000   Number         0  wdg.o ABSOLUTE
    ..\Middlewares\MALLOC\malloc.c           0x00000000   Number         0  malloc.o ABSOLUTE
    ..\Middlewares\USMART\usmart.c           0x00000000   Number         0  usmart.o ABSOLUTE
    ..\Middlewares\USMART\usmart_config.c    0x00000000   Number         0  usmart_config.o ABSOLUTE
    ..\Middlewares\USMART\usmart_port.c      0x00000000   Number         0  usmart_port.o ABSOLUTE
    ..\Middlewares\USMART\usmart_str.c       0x00000000   Number         0  usmart_str.o ABSOLUTE
    ..\Middlewares\cJSON\cJSON.c             0x00000000   Number         0  cjson.o ABSOLUTE
    ..\Middlewares\freertos\src\croutine.c   0x00000000   Number         0  croutine.o ABSOLUTE
    ..\Middlewares\freertos\src\event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ..\Middlewares\freertos\src\heap_4.c     0x00000000   Number         0  heap_4.o ABSOLUTE
    ..\Middlewares\freertos\src\list.c       0x00000000   Number         0  list.o ABSOLUTE
    ..\Middlewares\freertos\src\port.c       0x00000000   Number         0  port.o ABSOLUTE
    ..\Middlewares\freertos\src\queue.c      0x00000000   Number         0  queue.o ABSOLUTE
    ..\Middlewares\freertos\src\stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ..\Middlewares\freertos\src\tasks.c      0x00000000   Number         0  tasks.o ABSOLUTE
    ..\Middlewares\freertos\src\timers.c     0x00000000   Number         0  timers.o ABSOLUTE
    ..\Middlewares\lwrb\lwrb.c               0x00000000   Number         0  lwrb.o ABSOLUTE
    ..\Middlewares\lwrb\lwrb_ex.c            0x00000000   Number         0  lwrb_ex.o ABSOLUTE
    ..\Middlewares\pid\pid.c                 0x00000000   Number         0  pid.o ABSOLUTE
    ..\\App\\app_key.c                       0x00000000   Number         0  app_key.o ABSOLUTE
    ..\\App\\app_motor.c                     0x00000000   Number         0  app_motor.o ABSOLUTE
    ..\\App\\app_state.c                     0x00000000   Number         0  app_state.o ABSOLUTE
    ..\\App\\app_ui.c                        0x00000000   Number         0  app_ui.o ABSOLUTE
    ..\\App\\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\Drivers\\BSP\\DEBUG\\debug.c         0x00000000   Number         0  debug.o ABSOLUTE
    ..\\Drivers\\BSP\\ENCODER\\encoder.c     0x00000000   Number         0  encoder.o ABSOLUTE
    ..\\Drivers\\BSP\\JYxx\\jy60.c           0x00000000   Number         0  jy60.o ABSOLUTE
    ..\\Drivers\\BSP\\JYxx\\jyxx.c           0x00000000   Number         0  jyxx.o ABSOLUTE
    ..\\Drivers\\BSP\\KEY\\adc_key.c         0x00000000   Number         0  adc_key.o ABSOLUTE
    ..\\Drivers\\BSP\\KEY\\gpio_key.c        0x00000000   Number         0  gpio_key.o ABSOLUTE
    ..\\Drivers\\BSP\\KEY\\key.c             0x00000000   Number         0  key.o ABSOLUTE
    ..\\Drivers\\BSP\\LED\\led.c             0x00000000   Number         0  led.o ABSOLUTE
    ..\\Drivers\\BSP\\MOTOR\\motor.c         0x00000000   Number         0  motor.o ABSOLUTE
    ..\\Drivers\\BSP\\OLED\\OLED.c           0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Drivers\\BSP\\TB6612\\tb6612.c       0x00000000   Number         0  tb6612.o ABSOLUTE
    ..\\Drivers\\BSP\\TRACK\\track.c         0x00000000   Number         0  track.o ABSOLUTE
    ..\\Drivers\\BSP\\UlTraSound\\ultrasound.c 0x00000000   Number         0  ultrasound.o ABSOLUTE
    ..\\Drivers\\PORT\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\Drivers\\PORT\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\Drivers\\PORT\\dma.c                 0x00000000   Number         0  dma.o ABSOLUTE
    ..\\Drivers\\PORT\\gpio.c                0x00000000   Number         0  gpio.o ABSOLUTE
    ..\\Drivers\\PORT\\i2c.c                 0x00000000   Number         0  i2c.o ABSOLUTE
    ..\\Drivers\\PORT\\spi.c                 0x00000000   Number         0  spi.o ABSOLUTE
    ..\\Drivers\\PORT\\sys.c                 0x00000000   Number         0  sys.o ABSOLUTE
    ..\\Drivers\\PORT\\tim.c                 0x00000000   Number         0  tim.o ABSOLUTE
    ..\\Drivers\\PORT\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    ..\\Drivers\\PORT\\wdg.c                 0x00000000   Number         0  wdg.o ABSOLUTE
    ..\\Middlewares\\MALLOC\\malloc.c        0x00000000   Number         0  malloc.o ABSOLUTE
    ..\\Middlewares\\USMART\\usmart.c        0x00000000   Number         0  usmart.o ABSOLUTE
    ..\\Middlewares\\USMART\\usmart_config.c 0x00000000   Number         0  usmart_config.o ABSOLUTE
    ..\\Middlewares\\USMART\\usmart_port.c   0x00000000   Number         0  usmart_port.o ABSOLUTE
    ..\\Middlewares\\USMART\\usmart_str.c    0x00000000   Number         0  usmart_str.o ABSOLUTE
    ..\\Middlewares\\freertos\\src\\port.c   0x00000000   Number         0  port.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x080001a0   Section      190  port.o(.emb_text)
    $v0                                      0x080001a0   Number         0  port.o(.emb_text)
    .text                                    0x08000260   Section       44  startup_stm32f407xx.o(.text)
    $v0                                      0x08000260   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x0800028c   Section        0  memcpya.o(.text)
    .text                                    0x080002b0   Section        0  memseta.o(.text)
    .text                                    0x080002d4   Section        0  strlen.o(.text)
    .text                                    0x080002e2   Section        0  strcmp.o(.text)
    .text                                    0x080002fe   Section        0  strcpy.o(.text)
    .text                                    0x08000310   Section        0  dadd.o(.text)
    .text                                    0x0800045e   Section        0  dmul.o(.text)
    .text                                    0x08000542   Section        0  ddiv.o(.text)
    .text                                    0x08000620   Section        0  f2d.o(.text)
    .text                                    0x08000648   Section       48  cdrcmple.o(.text)
    .text                                    0x08000678   Section        0  uidiv.o(.text)
    .text                                    0x080006a4   Section        0  uldiv.o(.text)
    .text                                    0x08000706   Section        0  llshl.o(.text)
    .text                                    0x08000724   Section        0  llushr.o(.text)
    .text                                    0x08000744   Section        0  llsshr.o(.text)
    .text                                    0x08000768   Section        0  depilogue.o(.text)
    .text                                    0x08000768   Section        0  iusefp.o(.text)
    .text                                    0x08000822   Section        0  dfixul.o(.text)
    .text                                    0x08000854   Section       36  init.o(.text)
    .text                                    0x08000878   Section        0  __dczerorl2.o(.text)
    i.EXTI0_IRQHandler                       0x080008d0   Section        0  gpio.o(i.EXTI0_IRQHandler)
    i.EXTI15_10_IRQHandler                   0x080008f8   Section        0  gpio.o(i.EXTI15_10_IRQHandler)
    i.EXTI1_IRQHandler                       0x080009a0   Section        0  gpio.o(i.EXTI1_IRQHandler)
    i.EXTI2_IRQHandler                       0x080009c8   Section        0  gpio.o(i.EXTI2_IRQHandler)
    i.EXTI3_IRQHandler                       0x080009f0   Section        0  gpio.o(i.EXTI3_IRQHandler)
    i.EXTI4_IRQHandler                       0x08000a18   Section        0  gpio.o(i.EXTI4_IRQHandler)
    i.EXTI9_5_IRQHandler                     0x08000a40   Section        0  gpio.o(i.EXTI9_5_IRQHandler)
    i.HardFault_Handler                      0x08000ad0   Section        0  sys.o(i.HardFault_Handler)
    i.OLED_Init                              0x08000b3c   Section        0  oled.o(i.OLED_Init)
    i.OLED_Printf                            0x08000dac   Section        0  oled.o(i.OLED_Printf)
    i.OLED_ShowChar                          0x08000dd8   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x08000e18   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x08000f50   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x08001054   Section        0  oled.o(i.OLED_Update)
    i.PendSV_Handler                         0x080010c8   Section        0  sys.o(i.PendSV_Handler)
    i.SVC_Handler                            0x080010cc   Section        0  sys.o(i.SVC_Handler)
    i.SysTick_Handler                        0x080010d0   Section        0  sys.o(i.SysTick_Handler)
    i.TIM1_BRK_TIM9_IRQHandler               0x080010f8   Section        0  tim.o(i.TIM1_BRK_TIM9_IRQHandler)
    i.TIM1_CC_IRQHandler                     0x0800110c   Section        0  tim.o(i.TIM1_CC_IRQHandler)
    i.TIM1_TRG_COM_TIM11_IRQHandler          0x08001120   Section        0  tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler)
    i.TIM1_UP_TIM10_IRQHandler               0x08001144   Section        0  tim.o(i.TIM1_UP_TIM10_IRQHandler)
    i.TIM2_IRQHandler                        0x08001158   Section        0  tim.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x0800116c   Section        0  tim.o(i.TIM3_IRQHandler)
    i.TIM5_IRQHandler                        0x08001180   Section        0  tim.o(i.TIM5_IRQHandler)
    i.TIM6_DAC_IRQHandler                    0x08001194   Section        0  tim.o(i.TIM6_DAC_IRQHandler)
    i.TIM7_IRQHandler                        0x080011f4   Section        0  usmart_port.o(i.TIM7_IRQHandler)
    i.TIM8_BRK_TIM12_IRQHandler              0x08001230   Section        0  tim.o(i.TIM8_BRK_TIM12_IRQHandler)
    i.TIM8_CC_IRQHandler                     0x08001244   Section        0  tim.o(i.TIM8_CC_IRQHandler)
    i.TIM8_TRG_COM_TIM14_IRQHandler          0x08001258   Section        0  tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler)
    i.TIM8_UP_TIM13_IRQHandler               0x0800126c   Section        0  tim.o(i.TIM8_UP_TIM13_IRQHandler)
    i.UART4_IRQHandler                       0x08001280   Section        0  usart.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x080012ac   Section        0  usart.o(i.UART5_IRQHandler)
    i.USART1_IRQHandler                      0x080012d0   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080012f4   Section        0  usart.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08001320   Section        0  usart.o(i.USART3_IRQHandler)
    i.USART6_IRQHandler                      0x0800134c   Section        0  usart.o(i.USART6_IRQHandler)
    i.__0snprintf                            0x08001378   Section        0  printfa.o(i.__0snprintf)
    i.__0vsnprintf                           0x080013ac   Section        0  printfa.o(i.__0vsnprintf)
    i.__0vsprintf                            0x080013e0   Section        0  printfa.o(i.__0vsprintf)
    i.__ARM_common_memclr4_10                0x08001404   Section        0  usmart_str.o(i.__ARM_common_memclr4_10)
    i.__scatterload_copy                     0x0800140e   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800141c   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800141e   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x0800142c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0800142d   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080015b0   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080015b1   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08001c64   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08001c65   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08001c88   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08001c89   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08001cb6   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08001cb7   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x08001ccc   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08001ccd   Thumb Code    10  printfa.o(i._sputc)
    i.app_init                               0x08001cd8   Section        0  main.o(i.app_init)
    i.app_key_task                           0x08001dec   Section        0  app_key.o(i.app_key_task)
    app_key_task                             0x08001ded   Thumb Code   126  app_key.o(i.app_key_task)
    i.app_key_task_start                     0x08001e90   Section        0  app_key.o(i.app_key_task_start)
    i.app_state_task                         0x08001edc   Section        0  app_state.o(i.app_state_task)
    app_state_task                           0x08001edd   Thumb Code   158  app_state.o(i.app_state_task)
    i.app_state_task_start                   0x08002048   Section        0  app_state.o(i.app_state_task_start)
    i.app_ui_task                            0x08002090   Section        0  app_ui.o(i.app_ui_task)
    app_ui_task                              0x08002091   Thumb Code    90  app_ui.o(i.app_ui_task)
    i.app_ui_task_start                      0x08002108   Section        0  app_ui.o(i.app_ui_task_start)
    i.debug_error                            0x0800214c   Section        0  debug.o(i.debug_error)
    i.debug_init                             0x080021c4   Section        0  debug.o(i.debug_init)
    i.debug_message                          0x080022ac   Section        0  debug.o(i.debug_message)
    i.debug_printf                           0x08002324   Section        0  debug.o(i.debug_printf)
    i.delay_init                             0x08002384   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080023a8   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x080023e0   Section        0  delay.o(i.delay_us)
    i.encoder_init                           0x08002414   Section        0  encoder.o(i.encoder_init)
    i.get_debug_time                         0x08002450   Section        0  debug.o(i.get_debug_time)
    i.gpio_af_set                            0x080024c0   Section        0  gpio.o(i.gpio_af_set)
    i.gpio_key_get_state                     0x0800252c   Section        0  gpio_key.o(i.gpio_key_get_state)
    i.gpio_key_init                          0x08002544   Section        0  gpio_key.o(i.gpio_key_init)
    i.gpio_key_scan                          0x0800258c   Section        0  gpio_key.o(i.gpio_key_scan)
    i.gpio_pin_init                          0x080026b4   Section        0  gpio.o(i.gpio_pin_init)
    i.gpio_read_pin                          0x0800283c   Section        0  gpio.o(i.gpio_read_pin)
    i.gpio_toggle_pin                        0x08002858   Section        0  gpio.o(i.gpio_toggle_pin)
    i.gpio_write_pin                         0x08002870   Section        0  gpio.o(i.gpio_write_pin)
    i.i2c_init                               0x08002894   Section        0  i2c.o(i.i2c_init)
    i.i2c_send_byte                          0x080028c4   Section        0  i2c.o(i.i2c_send_byte)
    i.i2c_start                              0x08002940   Section        0  i2c.o(i.i2c_start)
    i.i2c_stop                               0x080029c4   Section        0  i2c.o(i.i2c_stop)
    i.i2c_wait_ack                           0x08002a44   Section        0  i2c.o(i.i2c_wait_ack)
    i.i2c_write_data                         0x08002aec   Section        0  i2c.o(i.i2c_write_data)
    i.jy60_get_acceleration                  0x08002b48   Section        0  jy60.o(i.jy60_get_acceleration)
    i.jy60_get_angle                         0x08002b50   Section        0  jy60.o(i.jy60_get_angle)
    i.jy60_get_angle_speed                   0x08002b58   Section        0  jy60.o(i.jy60_get_angle_speed)
    i.jy60_init                              0x08002b60   Section        0  jy60.o(i.jy60_init)
    i.jy60_send_command                      0x08002bf8   Section        0  jy60.o(i.jy60_send_command)
    i.jy60_uart_recv_callback                0x08002c14   Section        0  jy60.o(i.jy60_uart_recv_callback)
    jy60_uart_recv_callback                  0x08002c15   Thumb Code   136  jy60.o(i.jy60_uart_recv_callback)
    i.jyxx_init                              0x08002ca4   Section        0  jyxx.o(i.jyxx_init)
    i.jyxx_oled_show                         0x08002ca8   Section        0  jyxx.o(i.jyxx_oled_show)
    i.key_get_value                          0x08002db0   Section        0  key.o(i.key_get_value)
    i.key_init                               0x08002db4   Section        0  key.o(i.key_init)
    i.key_scan                               0x08002dcc   Section        0  key.o(i.key_scan)
    i.led_init                               0x08002dd0   Section        0  led.o(i.led_init)
    i.led_toggle                             0x08002e08   Section        0  led.o(i.led_toggle)
    i.main                                   0x08002e18   Section        0  main.o(i.main)
    i.motor_init                             0x08002ed4   Section        0  motor.o(i.motor_init)
    i.motor_set_speed                        0x08002ed8   Section        0  motor.o(i.motor_set_speed)
    i.my_mem_init                            0x08002f2c   Section        0  malloc.o(i.my_mem_init)
    i.my_mem_perused                         0x08002f64   Section        0  malloc.o(i.my_mem_perused)
    i.my_printf                              0x08002fa0   Section        0  debug.o(i.my_printf)
    i.prvAddCurrentTaskToDelayedList         0x08002fec   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08002fed   Thumb Code    90  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvIdleTask                            0x0800304c   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x0800304d   Thumb Code    88  tasks.o(i.prvIdleTask)
    i.prvListTasksWithinSingleList           0x080030b0   Section        0  tasks.o(i.prvListTasksWithinSingleList)
    prvListTasksWithinSingleList             0x080030b1   Thumb Code   242  tasks.o(i.prvListTasksWithinSingleList)
    i.prvProcessExpiredTimer                 0x080031b0   Section        0  timers.o(i.prvProcessExpiredTimer)
    prvProcessExpiredTimer                   0x080031b1   Thumb Code   156  timers.o(i.prvProcessExpiredTimer)
    i.prvSampleTimeNow                       0x08003250   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x08003251   Thumb Code   208  timers.o(i.prvSampleTimeNow)
    i.prvTaskExitError                       0x08003324   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08003325   Thumb Code    16  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x08003334   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08003335   Thumb Code   702  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x080035fc   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x080035fd   Thumb Code   108  queue.o(i.prvUnlockQueue)
    i.pvPortMalloc                           0x08003668   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxPortInitialiseStack                  0x08003760   Section        0  port.o(i.pxPortInitialiseStack)
    i.read_addr                              0x0800378c   Section        0  usmart.o(i.read_addr)
    i.sys_check_rst                          0x08003790   Section        0  sys.o(i.sys_check_rst)
    i.sys_clock_set                          0x080038bc   Section        0  sys.o(i.sys_clock_set)
    i.sys_get_tick                           0x080039e4   Section        0  sys.o(i.sys_get_tick)
    i.sys_nvic_init                          0x080039f0   Section        0  sys.o(i.sys_nvic_init)
    i.sys_soft_reset                         0x08003a60   Section        0  sys.o(i.sys_soft_reset)
    i.sys_stm32_clock_init                   0x08003a70   Section        0  sys.o(i.sys_stm32_clock_init)
    i.tb6612_init                            0x08003ab8   Section        0  tb6612.o(i.tb6612_init)
    i.tb6612_set_direction                   0x08003b50   Section        0  tb6612.o(i.tb6612_set_direction)
    i.tb6612_set_pwm_value                   0x08003bac   Section        0  tb6612.o(i.tb6612_set_pwm_value)
    i.tim_cap_get_value                      0x08003bc4   Section        0  tim.o(i.tim_cap_get_value)
    i.tim_cap_irq_callback                   0x08003bf0   Section        0  tim.o(i.tim_cap_irq_callback)
    tim_cap_irq_callback                     0x08003bf1   Thumb Code   292  tim.o(i.tim_cap_irq_callback)
    i.tim_cap_pin_init                       0x08003d4c   Section        0  tim.o(i.tim_cap_pin_init)
    i.tim_cnt_pin_init                       0x08003f8c   Section        0  tim.o(i.tim_cnt_pin_init)
    i.tim_cnt_set_value                      0x08004200   Section        0  tim.o(i.tim_cnt_set_value)
    i.tim_int_callback_register              0x080042a8   Section        0  tim.o(i.tim_int_callback_register)
    i.tim_pwm_pin_init                       0x08004360   Section        0  tim.o(i.tim_pwm_pin_init)
    i.tim_pwm_set_ccr                        0x08004578   Section        0  tim.o(i.tim_pwm_set_ccr)
    i.track_init                             0x0800463c   Section        0  track.o(i.track_init)
    i.track_oled_show                        0x08004648   Section        0  track.o(i.track_oled_show)
    i.ultrasound_init                        0x080046b8   Section        0  ultrasound.o(i.ultrasound_init)
    i.ultrasound_read_distance               0x08004704   Section        0  ultrasound.o(i.ultrasound_read_distance)
    i.ultrasound_tim_ic_handler              0x08004744   Section        0  ultrasound.o(i.ultrasound_tim_ic_handler)
    i.ultrasound_tim_overflow_handler        0x080047bc   Section        0  ultrasound.o(i.ultrasound_tim_overflow_handler)
    i.usart_init                             0x080047cc   Section        0  usart.o(i.usart_init)
    i.usart_iqr_callback_register            0x08004928   Section        0  usart.o(i.usart_iqr_callback_register)
    i.usart_send_data                        0x08004934   Section        0  usart.o(i.usart_send_data)
    i.usmart_cmd_rec                         0x080049bc   Section        0  usmart.o(i.usmart_cmd_rec)
    i.usmart_exe                             0x08004a6c   Section        0  usmart.o(i.usmart_exe)
    i.usmart_get_aparm                       0x08004cec   Section        0  usmart_str.o(i.usmart_get_aparm)
    i.usmart_get_cmdname                     0x08004da2   Section        0  usmart_str.o(i.usmart_get_cmdname)
    i.usmart_get_fname                       0x08004de4   Section        0  usmart_str.o(i.usmart_get_fname)
    i.usmart_get_fparam                      0x08004f98   Section        0  usmart_str.o(i.usmart_get_fparam)
    i.usmart_get_input_string                0x080052a0   Section        0  usmart_port.o(i.usmart_get_input_string)
    i.usmart_get_parmpos                     0x080052bc   Section        0  usmart_str.o(i.usmart_get_parmpos)
    i.usmart_init                            0x08005314   Section        0  usmart.o(i.usmart_init)
    i.usmart_recv_func                       0x08005334   Section        0  usmart_port.o(i.usmart_recv_func)
    i.usmart_scan                            0x08005378   Section        0  usmart.o(i.usmart_scan)
    i.usmart_str2num                         0x0800541c   Section        0  usmart_str.o(i.usmart_str2num)
    i.usmart_strcmp                          0x0800552e   Section        0  usmart_str.o(i.usmart_strcmp)
    i.usmart_sys_cmd_exe                     0x08005548   Section        0  usmart.o(i.usmart_sys_cmd_exe)
    i.usmart_timx_get_time                   0x08005be4   Section        0  usmart_port.o(i.usmart_timx_get_time)
    i.usmart_timx_init                       0x08005c18   Section        0  usmart_port.o(i.usmart_timx_init)
    i.usmart_timx_reset_time                 0x08005c70   Section        0  usmart_port.o(i.usmart_timx_reset_time)
    i.uxListRemove                           0x08005ca0   Section        0  list.o(i.uxListRemove)
    i.vListInitialise                        0x08005cc4   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x08005cda   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08005ce0   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x08005d1a   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x08005d34   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08005d50   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08005d68   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x08005df4   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vQueueWaitForMessageRestricted         0x08005e0c   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelete                            0x08005e58   Section        0  tasks.o(i.vTaskDelete)
    i.vTaskInternalSetTimeOutState           0x08005f28   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskListTasks                         0x08005f38   Section        0  tasks.o(i.vTaskListTasks)
    i.vTaskMissedYield                       0x080060bc   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x080060c8   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x080060e8   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskStartScheduler                    0x08006128   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x0800619c   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x080061ac   Section        0  tasks.o(i.vTaskSwitchContext)
    i.write_addr                             0x080061f8   Section        0  usmart.o(i.write_addr)
    i.xPortGetFreeHeapSize                   0x080061fc   Section        0  heap_4.o(i.xPortGetFreeHeapSize)
    i.xPortStartScheduler                    0x08006208   Section        0  port.o(i.xPortStartScheduler)
    i.xPortSysTickHandler                    0x08006248   Section        0  port.o(i.xPortSysTickHandler)
    i.xQueueGenericCreate                    0x08006274   Section        0  queue.o(i.xQueueGenericCreate)
    i.xQueueReceive                          0x08006308   Section        0  queue.o(i.xQueueReceive)
    i.xTaskCheckForTimeOut                   0x0800641c   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x08006474   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskDelayUntil                        0x08006618   Section        0  tasks.o(i.xTaskDelayUntil)
    i.xTaskGetSchedulerState                 0x08006674   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x08006690   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x0800669c   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskRemoveFromEventList               0x08006814   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x080068fc   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x08006a50   Section        0  timers.o(i.xTimerCreateTimerTask)
    .constdata                               0x08006ac0   Section     1152  gpio.o(.constdata)
    .constdata                               0x08006f40   Section       26  tim.o(.constdata)
    __func__                                 0x08006f40   Data          26  tim.o(.constdata)
    .constdata                               0x08006f5a   Section        8  led.o(.constdata)
    .constdata                               0x08006f62   Section     1520  oled_data.o(.constdata)
    .constdata                               0x08007552   Section      570  oled_data.o(.constdata)
    .constdata                               0x0800778c   Section      245  oled_data.o(.constdata)
    .constdata                               0x08007881   Section       18  gpio_key.o(.constdata)
    .constdata                               0x08007893   Section        6  encoder.o(.constdata)
    .constdata                               0x08007899   Section       10  tb6612.o(.constdata)
    .constdata                               0x080078a3   Section       13  app_key.o(.constdata)
    __func__                                 0x080078a3   Data          13  app_key.o(.constdata)
    .constdata                               0x080078b0   Section       12  malloc.o(.constdata)
    .conststring                             0x080078bc   Section       40  usmart.o(.conststring)
    .conststring                             0x080078e4   Section      220  usmart_config.o(.conststring)
    .data                                    0x20000000   Section        8  sys.o(.data)
    .data                                    0x20000008   Section        4  delay.o(.data)
    g_fac_us                                 0x20000008   Data           4  delay.o(.data)
    .data                                    0x2000000c   Section        8  i2c.o(.data)
    sda_pin                                  0x2000000c   Data           4  i2c.o(.data)
    scl_pin                                  0x20000010   Data           4  i2c.o(.data)
    .data                                    0x20000014   Section        4  tim.o(.data)
    count                                    0x20000016   Data           2  tim.o(.data)
    .data                                    0x20000018   Section       10  gpio_key.o(.data)
    last_key                                 0x20000018   Data           1  gpio_key.o(.data)
    debounce_count                           0x20000019   Data           1  gpio_key.o(.data)
    click_count                              0x2000001a   Data           1  gpio_key.o(.data)
    pending_key                              0x2000001b   Data           1  gpio_key.o(.data)
    multi_click_timer                        0x2000001c   Data           1  gpio_key.o(.data)
    key_state                                0x2000001d   Data           2  gpio_key.o(.data)
    press_duration                           0x20000020   Data           2  gpio_key.o(.data)
    .data                                    0x20000022   Section        1  track.o(.data)
    digital                                  0x20000022   Data           1  track.o(.data)
    .data                                    0x20000024   Section       16  jy60.o(.data)
    jy60_sleep_state                         0x20000024   Data           1  jy60.o(.data)
    jy60_data_ready                          0x20000025   Data           1  jy60.o(.data)
    jy60_buffer_index                        0x20000026   Data           1  jy60.o(.data)
    temperature                              0x20000028   Data           4  jy60.o(.data)
    voltage                                  0x2000002c   Data           4  jy60.o(.data)
    firmware_ver                             0x20000030   Data           4  jy60.o(.data)
    .data                                    0x20000034   Section        4  motor.o(.data)
    .data                                    0x20000038   Section        2  app_key.o(.data)
    is_app_key_task_create                   0x20000039   Data           1  app_key.o(.data)
    .data                                    0x2000003a   Section        2  app_state.o(.data)
    .data                                    0x2000003c   Section        1  app_ui.o(.data)
    .data                                    0x20000040   Section       20  malloc.o(.data)
    .data                                    0x20000054   Section       28  usmart.o(.data)
    .data                                    0x20000070   Section       48  usmart_config.o(.data)
    .data                                    0x200000a0   Section      244  usmart_config.o(.data)
    .data                                    0x20000194   Section        2  usmart_port.o(.data)
    .data                                    0x20000198   Section       28  heap_4.o(.data)
    pxEnd                                    0x20000198   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x2000019c   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x200001a0   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x200001a4   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x200001a8   Data           4  heap_4.o(.data)
    xStart                                   0x200001ac   Data           8  heap_4.o(.data)
    .data                                    0x200001b4   Section        4  port.o(.data)
    uxCriticalNesting                        0x200001b4   Data           4  port.o(.data)
    .data                                    0x200001b8   Section       64  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x200001bc   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x200001c0   Data           4  tasks.o(.data)
    xTickCount                               0x200001c4   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x200001c8   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x200001cc   Data           4  tasks.o(.data)
    xPendedTicks                             0x200001d0   Data           4  tasks.o(.data)
    xYieldPendings                           0x200001d4   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x200001d8   Data           4  tasks.o(.data)
    uxTaskNumber                             0x200001dc   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x200001e0   Data           4  tasks.o(.data)
    uxTopUsedPriority                        0x200001e4   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x200001e8   Data           4  tasks.o(.data)
    pxDelayedTaskList                        0x200001ec   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x200001f0   Data           4  tasks.o(.data)
    xIdleTaskHandles                         0x200001f4   Data           4  tasks.o(.data)
    .data                                    0x200001f8   Section       20  timers.o(.data)
    xTimerQueue                              0x200001f8   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x200001fc   Data           4  timers.o(.data)
    xLastTime                                0x20000200   Data           4  timers.o(.data)
    pxCurrentTimerList                       0x20000204   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x20000208   Data           4  timers.o(.data)
    .bss                                     0x2000020c   Section       64  gpio.o(.bss)
    .bss                                     0x2000024c   Section       28  usart.o(.bss)
    .bss                                     0x20000268   Section      228  tim.o(.bss)
    .bss                                     0x2000034c   Section     1056  debug.o(.bss)
    debug_time_str                           0x2000074c   Data          32  debug.o(.bss)
    .bss                                     0x2000076c   Section     1024  oled.o(.bss)
    .bss                                     0x20000b6c   Section      102  jy60.o(.bss)
    acceleration                             0x20000b6c   Data          12  jy60.o(.bss)
    angular_vel                              0x20000b78   Data          12  jy60.o(.bss)
    euler_angles                             0x20000b84   Data          12  jy60.o(.bss)
    jy60_raw_buffer                          0x20000b90   Data          33  jy60.o(.bss)
    jy60_processed_buffer                    0x20000bb1   Data          33  jy60.o(.bss)
    .bss                                     0x20000bd4   Section       20  ultrasound.o(.bss)
    Hcsr04Info                               0x20000bd4   Data          20  ultrasound.o(.bss)
    .bss                                     0x20000be8   Section       40  app_key.o(.bss)
    .bss                                     0x20000c10   Section      256  app_state.o(.bss)
    .bss                                     0x20000d40   Section    39232  malloc.o(.bss)
    mem1base                                 0x20000d40   Data       39232  malloc.o(.bss)
    .bss                                     0x2000a680   Section     2452  malloc.o(.bss)
    mem1mapbase                              0x2000a680   Data        2452  malloc.o(.bss)
    .bss                                     0x2000b014   Section      256  usmart_port.o(.bss)
    .bss                                     0x2000b114   Section    20480  heap_4.o(.bss)
    ucHeap                                   0x2000b114   Data       20480  heap_4.o(.bss)
    .bss                                     0x20010114   Section      180  tasks.o(.bss)
    pxReadyTasksLists                        0x20010114   Data         100  tasks.o(.bss)
    xDelayedTaskList1                        0x20010178   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x2001018c   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x200101a0   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x200101b4   Data          20  tasks.o(.bss)
    .bss                                     0x200101c8   Section       40  timers.o(.bss)
    xActiveTimerList1                        0x200101c8   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x200101dc   Data          20  timers.o(.bss)
    STACK                                    0x200101f0   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_int                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    vPortSVCHandler                          0x080001a1   Thumb Code    28  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x080001c1   Thumb Code    36  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvEnableVFP  0x080001e9   Thumb Code    16  port.o(.emb_text)
    xPortPendSVHandler                       0x080001fd   Thumb Code    88  port.o(.emb_text)
    vPortGetIPSR                             0x08000259   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x08000261   Thumb Code    14  startup_stm32f407xx.o(.text)
    NMI_Handler                              0x0800026f   Thumb Code     2  startup_stm32f407xx.o(.text)
    MemManage_Handler                        0x08000273   Thumb Code     2  startup_stm32f407xx.o(.text)
    BusFault_Handler                         0x08000275   Thumb Code     2  startup_stm32f407xx.o(.text)
    UsageFault_Handler                       0x08000277   Thumb Code     2  startup_stm32f407xx.o(.text)
    DebugMon_Handler                         0x0800027b   Thumb Code     2  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x08000281   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_memcpy                           0x0800028d   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800028d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800028d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x080002b1   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080002b1   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080002b1   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080002bf   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080002bf   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080002bf   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080002c3   Thumb Code    18  memseta.o(.text)
    strlen                                   0x080002d5   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x080002e3   Thumb Code    28  strcmp.o(.text)
    strcpy                                   0x080002ff   Thumb Code    18  strcpy.o(.text)
    __aeabi_dadd                             0x08000311   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000453   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000459   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800045f   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000543   Thumb Code   222  ddiv.o(.text)
    __aeabi_f2d                              0x08000621   Thumb Code    38  f2d.o(.text)
    __aeabi_cdrcmple                         0x08000649   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_uidiv                            0x08000679   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000679   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x080006a5   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08000707   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000707   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000725   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000725   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08000745   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000745   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08000769   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x08000769   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000787   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x08000823   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08000855   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000855   Thumb Code     0  init.o(.text)
    __decompress                             0x08000879   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000879   Thumb Code    86  __dczerorl2.o(.text)
    EXTI0_IRQHandler                         0x080008d1   Thumb Code    32  gpio.o(i.EXTI0_IRQHandler)
    EXTI15_10_IRQHandler                     0x080008f9   Thumb Code   158  gpio.o(i.EXTI15_10_IRQHandler)
    EXTI1_IRQHandler                         0x080009a1   Thumb Code    32  gpio.o(i.EXTI1_IRQHandler)
    EXTI2_IRQHandler                         0x080009c9   Thumb Code    32  gpio.o(i.EXTI2_IRQHandler)
    EXTI3_IRQHandler                         0x080009f1   Thumb Code    32  gpio.o(i.EXTI3_IRQHandler)
    EXTI4_IRQHandler                         0x08000a19   Thumb Code    32  gpio.o(i.EXTI4_IRQHandler)
    EXTI9_5_IRQHandler                       0x08000a41   Thumb Code   134  gpio.o(i.EXTI9_5_IRQHandler)
    HardFault_Handler                        0x08000ad1   Thumb Code    42  sys.o(i.HardFault_Handler)
    OLED_Init                                0x08000b3d   Thumb Code   618  oled.o(i.OLED_Init)
    OLED_Printf                              0x08000dad   Thumb Code    42  oled.o(i.OLED_Printf)
    OLED_ShowChar                            0x08000dd9   Thumb Code    56  oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x08000e19   Thumb Code   308  oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x08000f51   Thumb Code   250  oled.o(i.OLED_ShowString)
    OLED_Update                              0x08001055   Thumb Code   110  oled.o(i.OLED_Update)
    PendSV_Handler                           0x080010c9   Thumb Code     4  sys.o(i.PendSV_Handler)
    SVC_Handler                              0x080010cd   Thumb Code     4  sys.o(i.SVC_Handler)
    SysTick_Handler                          0x080010d1   Thumb Code    36  sys.o(i.SysTick_Handler)
    TIM1_BRK_TIM9_IRQHandler                 0x080010f9   Thumb Code    16  tim.o(i.TIM1_BRK_TIM9_IRQHandler)
    TIM1_CC_IRQHandler                       0x0800110d   Thumb Code    16  tim.o(i.TIM1_CC_IRQHandler)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08001121   Thumb Code    30  tim.o(i.TIM1_TRG_COM_TIM11_IRQHandler)
    TIM1_UP_TIM10_IRQHandler                 0x08001145   Thumb Code    16  tim.o(i.TIM1_UP_TIM10_IRQHandler)
    TIM2_IRQHandler                          0x08001159   Thumb Code    16  tim.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x0800116d   Thumb Code    16  tim.o(i.TIM3_IRQHandler)
    TIM5_IRQHandler                          0x08001181   Thumb Code    16  tim.o(i.TIM5_IRQHandler)
    TIM6_DAC_IRQHandler                      0x08001195   Thumb Code    84  tim.o(i.TIM6_DAC_IRQHandler)
    TIM7_IRQHandler                          0x080011f5   Thumb Code    42  usmart_port.o(i.TIM7_IRQHandler)
    TIM8_BRK_TIM12_IRQHandler                0x08001231   Thumb Code    16  tim.o(i.TIM8_BRK_TIM12_IRQHandler)
    TIM8_CC_IRQHandler                       0x08001245   Thumb Code    16  tim.o(i.TIM8_CC_IRQHandler)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08001259   Thumb Code    16  tim.o(i.TIM8_TRG_COM_TIM14_IRQHandler)
    TIM8_UP_TIM13_IRQHandler                 0x0800126d   Thumb Code    16  tim.o(i.TIM8_UP_TIM13_IRQHandler)
    UART4_IRQHandler                         0x08001281   Thumb Code    30  usart.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x080012ad   Thumb Code    28  usart.o(i.UART5_IRQHandler)
    USART1_IRQHandler                        0x080012d1   Thumb Code    28  usart.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080012f5   Thumb Code    30  usart.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08001321   Thumb Code    30  usart.o(i.USART3_IRQHandler)
    USART6_IRQHandler                        0x0800134d   Thumb Code    30  usart.o(i.USART6_IRQHandler)
    __0snprintf                              0x08001379   Thumb Code    48  printfa.o(i.__0snprintf)
    __1snprintf                              0x08001379   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x08001379   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x08001379   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x08001379   Thumb Code     0  printfa.o(i.__0snprintf)
    __0vsnprintf                             0x080013ad   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x080013ad   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x080013ad   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x080013ad   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x080013ad   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __0vsprintf                              0x080013e1   Thumb Code    30  printfa.o(i.__0vsprintf)
    __1vsprintf                              0x080013e1   Thumb Code     0  printfa.o(i.__0vsprintf)
    __2vsprintf                              0x080013e1   Thumb Code     0  printfa.o(i.__0vsprintf)
    __c89vsprintf                            0x080013e1   Thumb Code     0  printfa.o(i.__0vsprintf)
    vsprintf                                 0x080013e1   Thumb Code     0  printfa.o(i.__0vsprintf)
    __ARM_common_memclr4_10                  0x08001405   Thumb Code    10  usmart_str.o(i.__ARM_common_memclr4_10)
    __scatterload_copy                       0x0800140f   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800141d   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800141f   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    app_init                                 0x08001cd9   Thumb Code    60  main.o(i.app_init)
    app_key_task_start                       0x08001e91   Thumb Code    48  app_key.o(i.app_key_task_start)
    app_state_task_start                     0x08002049   Thumb Code    48  app_state.o(i.app_state_task_start)
    app_ui_task_start                        0x08002109   Thumb Code    48  app_ui.o(i.app_ui_task_start)
    debug_error                              0x0800214d   Thumb Code    98  debug.o(i.debug_error)
    debug_init                               0x080021c5   Thumb Code   142  debug.o(i.debug_init)
    debug_message                            0x080022ad   Thumb Code    98  debug.o(i.debug_message)
    debug_printf                             0x08002325   Thumb Code    88  debug.o(i.debug_printf)
    delay_init                               0x08002385   Thumb Code    32  delay.o(i.delay_init)
    delay_ms                                 0x080023a9   Thumb Code    52  delay.o(i.delay_ms)
    delay_us                                 0x080023e1   Thumb Code    46  delay.o(i.delay_us)
    encoder_init                             0x08002415   Thumb Code    54  encoder.o(i.encoder_init)
    get_debug_time                           0x08002451   Thumb Code    78  debug.o(i.get_debug_time)
    gpio_af_set                              0x080024c1   Thumb Code   102  gpio.o(i.gpio_af_set)
    gpio_key_get_state                       0x0800252d   Thumb Code    20  gpio_key.o(i.gpio_key_get_state)
    gpio_key_init                            0x08002545   Thumb Code    66  gpio_key.o(i.gpio_key_init)
    gpio_key_scan                            0x0800258d   Thumb Code   280  gpio_key.o(i.gpio_key_scan)
    gpio_pin_init                            0x080026b5   Thumb Code   384  gpio.o(i.gpio_pin_init)
    gpio_read_pin                            0x0800283d   Thumb Code    22  gpio.o(i.gpio_read_pin)
    gpio_toggle_pin                          0x08002859   Thumb Code    20  gpio.o(i.gpio_toggle_pin)
    gpio_write_pin                           0x08002871   Thumb Code    30  gpio.o(i.gpio_write_pin)
    i2c_init                                 0x08002895   Thumb Code    44  i2c.o(i.i2c_init)
    i2c_send_byte                            0x080028c5   Thumb Code   118  i2c.o(i.i2c_send_byte)
    i2c_start                                0x08002941   Thumb Code   126  i2c.o(i.i2c_start)
    i2c_stop                                 0x080029c5   Thumb Code   118  i2c.o(i.i2c_stop)
    i2c_wait_ack                             0x08002a45   Thumb Code   158  i2c.o(i.i2c_wait_ack)
    i2c_write_data                           0x08002aed   Thumb Code    92  i2c.o(i.i2c_write_data)
    jy60_get_acceleration                    0x08002b49   Thumb Code     4  jy60.o(i.jy60_get_acceleration)
    jy60_get_angle                           0x08002b51   Thumb Code     4  jy60.o(i.jy60_get_angle)
    jy60_get_angle_speed                     0x08002b59   Thumb Code     4  jy60.o(i.jy60_get_angle_speed)
    jy60_init                                0x08002b61   Thumb Code   142  jy60.o(i.jy60_init)
    jy60_send_command                        0x08002bf9   Thumb Code    24  jy60.o(i.jy60_send_command)
    jyxx_init                                0x08002ca5   Thumb Code     4  jyxx.o(i.jyxx_init)
    jyxx_oled_show                           0x08002ca9   Thumb Code   168  jyxx.o(i.jyxx_oled_show)
    key_get_value                            0x08002db1   Thumb Code     4  key.o(i.key_get_value)
    key_init                                 0x08002db5   Thumb Code    18  key.o(i.key_init)
    key_scan                                 0x08002dcd   Thumb Code     4  key.o(i.key_scan)
    led_init                                 0x08002dd1   Thumb Code    52  led.o(i.led_init)
    led_toggle                               0x08002e09   Thumb Code    12  led.o(i.led_toggle)
    main                                     0x08002e19   Thumb Code   152  main.o(i.main)
    motor_init                               0x08002ed5   Thumb Code     4  motor.o(i.motor_init)
    motor_set_speed                          0x08002ed9   Thumb Code    80  motor.o(i.motor_set_speed)
    my_mem_init                              0x08002f2d   Thumb Code    48  malloc.o(i.my_mem_init)
    my_mem_perused                           0x08002f65   Thumb Code    52  malloc.o(i.my_mem_perused)
    my_printf                                0x08002fa1   Thumb Code    70  debug.o(i.my_printf)
    pvPortMalloc                             0x08003669   Thumb Code   236  heap_4.o(i.pvPortMalloc)
    pxPortInitialiseStack                    0x08003761   Thumb Code    38  port.o(i.pxPortInitialiseStack)
    read_addr                                0x0800378d   Thumb Code     4  usmart.o(i.read_addr)
    sys_check_rst                            0x08003791   Thumb Code   118  sys.o(i.sys_check_rst)
    sys_clock_set                            0x080038bd   Thumb Code   270  sys.o(i.sys_clock_set)
    sys_get_tick                             0x080039e5   Thumb Code     8  sys.o(i.sys_get_tick)
    sys_nvic_init                            0x080039f1   Thumb Code   106  sys.o(i.sys_nvic_init)
    sys_soft_reset                           0x08003a61   Thumb Code     8  sys.o(i.sys_soft_reset)
    sys_stm32_clock_init                     0x08003a71   Thumb Code    56  sys.o(i.sys_stm32_clock_init)
    tb6612_init                              0x08003ab9   Thumb Code   142  tb6612.o(i.tb6612_init)
    tb6612_set_direction                     0x08003b51   Thumb Code    88  tb6612.o(i.tb6612_set_direction)
    tb6612_set_pwm_value                     0x08003bad   Thumb Code    14  tb6612.o(i.tb6612_set_pwm_value)
    tim_cap_get_value                        0x08003bc5   Thumb Code    38  tim.o(i.tim_cap_get_value)
    tim_cap_pin_init                         0x08003d4d   Thumb Code   514  tim.o(i.tim_cap_pin_init)
    tim_cnt_pin_init                         0x08003f8d   Thumb Code   570  tim.o(i.tim_cnt_pin_init)
    tim_cnt_set_value                        0x08004201   Thumb Code   116  tim.o(i.tim_cnt_set_value)
    tim_int_callback_register                0x080042a9   Thumb Code   108  tim.o(i.tim_int_callback_register)
    tim_pwm_pin_init                         0x08004361   Thumb Code   478  tim.o(i.tim_pwm_pin_init)
    tim_pwm_set_ccr                          0x08004579   Thumb Code   144  tim.o(i.tim_pwm_set_ccr)
    track_init                               0x0800463d   Thumb Code    10  track.o(i.track_init)
    track_oled_show                          0x08004649   Thumb Code    72  track.o(i.track_oled_show)
    ultrasound_init                          0x080046b9   Thumb Code    66  ultrasound.o(i.ultrasound_init)
    ultrasound_read_distance                 0x08004705   Thumb Code    50  ultrasound.o(i.ultrasound_read_distance)
    ultrasound_tim_ic_handler                0x08004745   Thumb Code   102  ultrasound.o(i.ultrasound_tim_ic_handler)
    ultrasound_tim_overflow_handler          0x080047bd   Thumb Code    10  ultrasound.o(i.ultrasound_tim_overflow_handler)
    usart_init                               0x080047cd   Thumb Code   316  usart.o(i.usart_init)
    usart_iqr_callback_register              0x08004929   Thumb Code     8  usart.o(i.usart_iqr_callback_register)
    usart_send_data                          0x08004935   Thumb Code   110  usart.o(i.usart_send_data)
    usmart_cmd_rec                           0x080049bd   Thumb Code   172  usmart.o(i.usmart_cmd_rec)
    usmart_exe                               0x08004a6d   Thumb Code   538  usmart.o(i.usmart_exe)
    usmart_get_aparm                         0x08004ced   Thumb Code   182  usmart_str.o(i.usmart_get_aparm)
    usmart_get_cmdname                       0x08004da3   Thumb Code    66  usmart_str.o(i.usmart_get_cmdname)
    usmart_get_fname                         0x08004de5   Thumb Code   426  usmart_str.o(i.usmart_get_fname)
    usmart_get_fparam                        0x08004f99   Thumb Code   768  usmart_str.o(i.usmart_get_fparam)
    usmart_get_input_string                  0x080052a1   Thumb Code    20  usmart_port.o(i.usmart_get_input_string)
    usmart_get_parmpos                       0x080052bd   Thumb Code    84  usmart_str.o(i.usmart_get_parmpos)
    usmart_init                              0x08005315   Thumb Code    24  usmart.o(i.usmart_init)
    usmart_recv_func                         0x08005335   Thumb Code    60  usmart_port.o(i.usmart_recv_func)
    usmart_scan                              0x08005379   Thumb Code   104  usmart.o(i.usmart_scan)
    usmart_str2num                           0x0800541d   Thumb Code   274  usmart_str.o(i.usmart_str2num)
    usmart_strcmp                            0x0800552f   Thumb Code    26  usmart_str.o(i.usmart_strcmp)
    usmart_sys_cmd_exe                       0x08005549   Thumb Code  1488  usmart.o(i.usmart_sys_cmd_exe)
    usmart_timx_get_time                     0x08005be5   Thumb Code    38  usmart_port.o(i.usmart_timx_get_time)
    usmart_timx_init                         0x08005c19   Thumb Code    70  usmart_port.o(i.usmart_timx_init)
    usmart_timx_reset_time                   0x08005c71   Thumb Code    32  usmart_port.o(i.usmart_timx_reset_time)
    uxListRemove                             0x08005ca1   Thumb Code    36  list.o(i.uxListRemove)
    vListInitialise                          0x08005cc5   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x08005cdb   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08005ce1   Thumb Code    58  list.o(i.vListInsert)
    vListInsertEnd                           0x08005d1b   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x08005d35   Thumb Code    24  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08005d51   Thumb Code    20  port.o(i.vPortExitCritical)
    vPortFree                                0x08005d69   Thumb Code   136  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x08005df5   Thumb Code    20  port.o(i.vPortSetupTimerInterrupt)
    vQueueWaitForMessageRestricted           0x08005e0d   Thumb Code    74  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelete                              0x08005e59   Thumb Code   190  tasks.o(i.vTaskDelete)
    vTaskInternalSetTimeOutState             0x08005f29   Thumb Code    12  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskListTasks                           0x08005f39   Thumb Code   358  tasks.o(i.vTaskListTasks)
    vTaskMissedYield                         0x080060bd   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x080060c9   Thumb Code    28  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x080060e9   Thumb Code    60  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskStartScheduler                      0x08006129   Thumb Code    96  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x0800619d   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x080061ad   Thumb Code    68  tasks.o(i.vTaskSwitchContext)
    write_addr                               0x080061f9   Thumb Code     4  usmart.o(i.write_addr)
    xPortGetFreeHeapSize                     0x080061fd   Thumb Code     6  heap_4.o(i.xPortGetFreeHeapSize)
    xPortStartScheduler                      0x08006209   Thumb Code    52  port.o(i.xPortStartScheduler)
    xPortSysTickHandler                      0x08006249   Thumb Code    38  port.o(i.xPortSysTickHandler)
    xQueueGenericCreate                      0x08006275   Thumb Code   148  queue.o(i.xQueueGenericCreate)
    xQueueReceive                            0x08006309   Thumb Code   272  queue.o(i.xQueueReceive)
    xTaskCheckForTimeOut                     0x0800641d   Thumb Code    82  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x08006475   Thumb Code   390  tasks.o(i.xTaskCreate)
    xTaskDelayUntil                          0x08006619   Thumb Code    84  tasks.o(i.xTaskDelayUntil)
    xTaskGetSchedulerState                   0x08006675   Thumb Code    24  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x08006691   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x0800669d   Thumb Code   362  tasks.o(i.xTaskIncrementTick)
    xTaskRemoveFromEventList                 0x08006815   Thumb Code   214  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x080068fd   Thumb Code   322  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x08006a51   Thumb Code    84  timers.o(i.xTimerCreateTimerTask)
    gpio_map                                 0x08006ac0   Data        1152  gpio.o(.constdata)
    led_pin                                  0x08006f5a   Data           4  led.o(.constdata)
    led_polarity                             0x08006f5e   Data           4  led.o(.constdata)
    OLED_F8x16                               0x08006f62   Data        1520  oled_data.o(.constdata)
    OLED_F6x8                                0x08007552   Data         570  oled_data.o(.constdata)
    OLED_CF16x16                             0x0800778c   Data         245  oled_data.o(.constdata)
    gpio_key_pin                             0x08007881   Data           6  gpio_key.o(.constdata)
    gpio_key_value                           0x08007887   Data           6  gpio_key.o(.constdata)
    gpio_key_polarity                        0x0800788d   Data           6  gpio_key.o(.constdata)
    encoder_tim_id                           0x08007893   Data           2  encoder.o(.constdata)
    encoder_pin                              0x08007895   Data           4  encoder.o(.constdata)
    tb6612_pwm_pin                           0x08007899   Data           2  tb6612.o(.constdata)
    tb6612_pwm_tim_id                        0x0800789b   Data           2  tb6612.o(.constdata)
    tb6612_pwm_tim_ch                        0x0800789d   Data           2  tb6612.o(.constdata)
    tb6612_control_pins                      0x0800789f   Data           4  tb6612.o(.constdata)
    memtblsize                               0x080078b0   Data           4  malloc.o(.constdata)
    memblksize                               0x080078b4   Data           4  malloc.o(.constdata)
    memsize                                  0x080078b8   Data           4  malloc.o(.constdata)
    Region$$Table$$Base                      0x080079c0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080079e0   Number         0  anon$$obj.o(Region$$Table)
    uwTick                                   0x20000000   Data           8  sys.o(.data)
    g_bitmx_callback_funs_num                0x20000014   Data           1  tim.o(.data)
    motor_dead_band_speed                    0x20000034   Data           4  motor.o(.data)
    key_callback_num                         0x20000038   Data           1  app_key.o(.data)
    is_app_state_task_start                  0x2000003a   Data           1  app_state.o(.data)
    sys_state                                0x2000003b   Data           1  app_state.o(.data)
    is_app_ui_task_start                     0x2000003c   Data           1  app_ui.o(.data)
    mallco_dev                               0x20000040   Data          20  malloc.o(.data)
    sys_cmd_tab                              0x20000054   Data          28  usmart.o(.data)
    usmart_nametab                           0x20000070   Data          48  usmart_config.o(.data)
    usmart_dev                               0x200000a0   Data         244  usmart_config.o(.data)
    usmart_recv_cnt                          0x20000194   Data           1  usmart_port.o(.data)
    usmart_recv_flag                         0x20000195   Data           1  usmart_port.o(.data)
    pxCurrentTCB                             0x200001b8   Data           4  tasks.o(.data)
    gpio_ex_callback_funs                    0x2000020c   Data          64  gpio.o(.bss)
    g_usart_callback_funs                    0x2000024c   Data          28  usart.o(.bss)
    g_timxchy_cap_sta                        0x20000268   Data          15  tim.o(.bss)
    g_timxchy_cap_channel                    0x20000277   Data          15  tim.o(.bss)
    g_timxchy_cap_val                        0x20000288   Data          60  tim.o(.bss)
    g_timxchy_cap_en                         0x200002c4   Data          15  tim.o(.bss)
    g_timx_callback_funs                     0x200002d4   Data          80  tim.o(.bss)
    g_bitmx_callback_funs_time               0x20000324   Data          40  tim.o(.bss)
    log_str                                  0x2000034c   Data        1024  debug.o(.bss)
    OLED_DisplayBuf                          0x2000076c   Data        1024  oled.o(.bss)
    key_callback_funs                        0x20000be8   Data          40  app_key.o(.bss)
    task_log                                 0x20000c10   Data         256  app_state.o(.bss)
    usmart_recv_buf                          0x2000b014   Data         256  usmart_port.o(.bss)
    __initial_sp                             0x200105f0   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007bec, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x00007a5c])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000079e0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         4001  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         4368    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         4371    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4373    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         4375    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         4376    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         4383    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         4378    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         4380    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         4369    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x000000be   Code   RO         2538    .emb_text           port.o
    0x0800025e   0x0800025e   0x00000002   PAD
    0x08000260   0x08000260   0x0000002c   Code   RO            4    .text               startup_stm32f407xx.o
    0x0800028c   0x0800028c   0x00000024   Code   RO         4006    .text               mc_w.l(memcpya.o)
    0x080002b0   0x080002b0   0x00000024   Code   RO         4008    .text               mc_w.l(memseta.o)
    0x080002d4   0x080002d4   0x0000000e   Code   RO         4010    .text               mc_w.l(strlen.o)
    0x080002e2   0x080002e2   0x0000001c   Code   RO         4012    .text               mc_w.l(strcmp.o)
    0x080002fe   0x080002fe   0x00000012   Code   RO         4014    .text               mc_w.l(strcpy.o)
    0x08000310   0x08000310   0x0000014e   Code   RO         4315    .text               mf_w.l(dadd.o)
    0x0800045e   0x0800045e   0x000000e4   Code   RO         4317    .text               mf_w.l(dmul.o)
    0x08000542   0x08000542   0x000000de   Code   RO         4319    .text               mf_w.l(ddiv.o)
    0x08000620   0x08000620   0x00000026   Code   RO         4331    .text               mf_w.l(f2d.o)
    0x08000646   0x08000646   0x00000002   PAD
    0x08000648   0x08000648   0x00000030   Code   RO         4335    .text               mf_w.l(cdrcmple.o)
    0x08000678   0x08000678   0x0000002c   Code   RO         4385    .text               mc_w.l(uidiv.o)
    0x080006a4   0x080006a4   0x00000062   Code   RO         4387    .text               mc_w.l(uldiv.o)
    0x08000706   0x08000706   0x0000001e   Code   RO         4389    .text               mc_w.l(llshl.o)
    0x08000724   0x08000724   0x00000020   Code   RO         4391    .text               mc_w.l(llushr.o)
    0x08000744   0x08000744   0x00000024   Code   RO         4393    .text               mc_w.l(llsshr.o)
    0x08000768   0x08000768   0x00000000   Code   RO         4410    .text               mc_w.l(iusefp.o)
    0x08000768   0x08000768   0x000000ba   Code   RO         4413    .text               mf_w.l(depilogue.o)
    0x08000822   0x08000822   0x00000030   Code   RO         4419    .text               mf_w.l(dfixul.o)
    0x08000852   0x08000852   0x00000002   PAD
    0x08000854   0x08000854   0x00000024   Code   RO         4425    .text               mc_w.l(init.o)
    0x08000878   0x08000878   0x00000056   Code   RO         4442    .text               mc_w.l(__dczerorl2.o)
    0x080008ce   0x080008ce   0x00000002   PAD
    0x080008d0   0x080008d0   0x00000028   Code   RO           13    i.EXTI0_IRQHandler  gpio.o
    0x080008f8   0x080008f8   0x000000a8   Code   RO           14    i.EXTI15_10_IRQHandler  gpio.o
    0x080009a0   0x080009a0   0x00000028   Code   RO           15    i.EXTI1_IRQHandler  gpio.o
    0x080009c8   0x080009c8   0x00000028   Code   RO           16    i.EXTI2_IRQHandler  gpio.o
    0x080009f0   0x080009f0   0x00000028   Code   RO           17    i.EXTI3_IRQHandler  gpio.o
    0x08000a18   0x08000a18   0x00000028   Code   RO           18    i.EXTI4_IRQHandler  gpio.o
    0x08000a40   0x08000a40   0x00000090   Code   RO           19    i.EXTI9_5_IRQHandler  gpio.o
    0x08000ad0   0x08000ad0   0x0000006c   Code   RO          190    i.HardFault_Handler  sys.o
    0x08000b3c   0x08000b3c   0x00000270   Code   RO          998    i.OLED_Init         oled.o
    0x08000dac   0x08000dac   0x0000002a   Code   RO         1001    i.OLED_Printf       oled.o
    0x08000dd6   0x08000dd6   0x00000002   PAD
    0x08000dd8   0x08000dd8   0x00000040   Code   RO         1006    i.OLED_ShowChar     oled.o
    0x08000e18   0x08000e18   0x00000138   Code   RO         1009    i.OLED_ShowImage    oled.o
    0x08000f50   0x08000f50   0x00000104   Code   RO         1012    i.OLED_ShowString   oled.o
    0x08001054   0x08001054   0x00000074   Code   RO         1013    i.OLED_Update       oled.o
    0x080010c8   0x080010c8   0x00000004   Code   RO          191    i.PendSV_Handler    sys.o
    0x080010cc   0x080010cc   0x00000004   Code   RO          192    i.SVC_Handler       sys.o
    0x080010d0   0x080010d0   0x00000028   Code   RO          193    i.SysTick_Handler   sys.o
    0x080010f8   0x080010f8   0x00000014   Code   RO          673    i.TIM1_BRK_TIM9_IRQHandler  tim.o
    0x0800110c   0x0800110c   0x00000014   Code   RO          674    i.TIM1_CC_IRQHandler  tim.o
    0x08001120   0x08001120   0x00000024   Code   RO          675    i.TIM1_TRG_COM_TIM11_IRQHandler  tim.o
    0x08001144   0x08001144   0x00000014   Code   RO          676    i.TIM1_UP_TIM10_IRQHandler  tim.o
    0x08001158   0x08001158   0x00000014   Code   RO          677    i.TIM2_IRQHandler   tim.o
    0x0800116c   0x0800116c   0x00000014   Code   RO          678    i.TIM3_IRQHandler   tim.o
    0x08001180   0x08001180   0x00000014   Code   RO          679    i.TIM5_IRQHandler   tim.o
    0x08001194   0x08001194   0x00000060   Code   RO          680    i.TIM6_DAC_IRQHandler  tim.o
    0x080011f4   0x080011f4   0x0000003c   Code   RO         2195    i.TIM7_IRQHandler   usmart_port.o
    0x08001230   0x08001230   0x00000014   Code   RO          681    i.TIM8_BRK_TIM12_IRQHandler  tim.o
    0x08001244   0x08001244   0x00000014   Code   RO          682    i.TIM8_CC_IRQHandler  tim.o
    0x08001258   0x08001258   0x00000014   Code   RO          683    i.TIM8_TRG_COM_TIM14_IRQHandler  tim.o
    0x0800126c   0x0800126c   0x00000014   Code   RO          684    i.TIM8_UP_TIM13_IRQHandler  tim.o
    0x08001280   0x08001280   0x0000002c   Code   RO          377    i.UART4_IRQHandler  usart.o
    0x080012ac   0x080012ac   0x00000024   Code   RO          378    i.UART5_IRQHandler  usart.o
    0x080012d0   0x080012d0   0x00000024   Code   RO          379    i.USART1_IRQHandler  usart.o
    0x080012f4   0x080012f4   0x0000002c   Code   RO          380    i.USART2_IRQHandler  usart.o
    0x08001320   0x08001320   0x0000002c   Code   RO          381    i.USART3_IRQHandler  usart.o
    0x0800134c   0x0800134c   0x0000002c   Code   RO          382    i.USART6_IRQHandler  usart.o
    0x08001378   0x08001378   0x00000034   Code   RO         4254    i.__0snprintf       mc_w.l(printfa.o)
    0x080013ac   0x080013ac   0x00000034   Code   RO         4258    i.__0vsnprintf      mc_w.l(printfa.o)
    0x080013e0   0x080013e0   0x00000024   Code   RO         4259    i.__0vsprintf       mc_w.l(printfa.o)
    0x08001404   0x08001404   0x0000000a   Code   RO         2323    i.__ARM_common_memclr4_10  usmart_str.o
    0x0800140e   0x0800140e   0x0000000e   Code   RO         4436    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800141c   0x0800141c   0x00000002   Code   RO         4437    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800141e   0x0800141e   0x0000000e   Code   RO         4438    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800142c   0x0800142c   0x00000184   Code   RO         4260    i._fp_digits        mc_w.l(printfa.o)
    0x080015b0   0x080015b0   0x000006b4   Code   RO         4261    i._printf_core      mc_w.l(printfa.o)
    0x08001c64   0x08001c64   0x00000024   Code   RO         4262    i._printf_post_padding  mc_w.l(printfa.o)
    0x08001c88   0x08001c88   0x0000002e   Code   RO         4263    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08001cb6   0x08001cb6   0x00000016   Code   RO         4264    i._snputc           mc_w.l(printfa.o)
    0x08001ccc   0x08001ccc   0x0000000a   Code   RO         4265    i._sputc            mc_w.l(printfa.o)
    0x08001cd6   0x08001cd6   0x00000002   PAD
    0x08001cd8   0x08001cd8   0x00000114   Code   RO         1739    i.app_init          main.o
    0x08001dec   0x08001dec   0x000000a4   Code   RO         1816    i.app_key_task      app_key.o
    0x08001e90   0x08001e90   0x0000004c   Code   RO         1817    i.app_key_task_start  app_key.o
    0x08001edc   0x08001edc   0x0000016c   Code   RO         1888    i.app_state_task    app_state.o
    0x08002048   0x08002048   0x00000048   Code   RO         1889    i.app_state_task_start  app_state.o
    0x08002090   0x08002090   0x00000078   Code   RO         1930    i.app_ui_task       app_ui.o
    0x08002108   0x08002108   0x00000044   Code   RO         1931    i.app_ui_task_start  app_ui.o
    0x0800214c   0x0800214c   0x00000078   Code   RO          910    i.debug_error       debug.o
    0x080021c4   0x080021c4   0x000000e8   Code   RO          911    i.debug_init        debug.o
    0x080022ac   0x080022ac   0x00000078   Code   RO          912    i.debug_message     debug.o
    0x08002324   0x08002324   0x00000060   Code   RO          913    i.debug_printf      debug.o
    0x08002384   0x08002384   0x00000024   Code   RO          339    i.delay_init        delay.o
    0x080023a8   0x080023a8   0x00000038   Code   RO          340    i.delay_ms          delay.o
    0x080023e0   0x080023e0   0x00000034   Code   RO          341    i.delay_us          delay.o
    0x08002414   0x08002414   0x0000003c   Code   RO         1324    i.encoder_init      encoder.o
    0x08002450   0x08002450   0x00000070   Code   RO          916    i.get_debug_time    debug.o
    0x080024c0   0x080024c0   0x0000006c   Code   RO           20    i.gpio_af_set       gpio.o
    0x0800252c   0x0800252c   0x00000018   Code   RO         1238    i.gpio_key_get_state  gpio_key.o
    0x08002544   0x08002544   0x00000048   Code   RO         1239    i.gpio_key_init     gpio_key.o
    0x0800258c   0x0800258c   0x00000128   Code   RO         1241    i.gpio_key_scan     gpio_key.o
    0x080026b4   0x080026b4   0x00000188   Code   RO           24    i.gpio_pin_init     gpio.o
    0x0800283c   0x0800283c   0x0000001c   Code   RO           25    i.gpio_read_pin     gpio.o
    0x08002858   0x08002858   0x00000018   Code   RO           26    i.gpio_toggle_pin   gpio.o
    0x08002870   0x08002870   0x00000024   Code   RO           27    i.gpio_write_pin    gpio.o
    0x08002894   0x08002894   0x00000030   Code   RO          477    i.i2c_init          i2c.o
    0x080028c4   0x080028c4   0x0000007c   Code   RO          481    i.i2c_send_byte     i2c.o
    0x08002940   0x08002940   0x00000084   Code   RO          482    i.i2c_start         i2c.o
    0x080029c4   0x080029c4   0x00000080   Code   RO          483    i.i2c_stop          i2c.o
    0x08002a44   0x08002a44   0x000000a8   Code   RO          484    i.i2c_wait_ack      i2c.o
    0x08002aec   0x08002aec   0x0000005c   Code   RO          485    i.i2c_write_data    i2c.o
    0x08002b48   0x08002b48   0x00000008   Code   RO         1417    i.jy60_get_acceleration  jy60.o
    0x08002b50   0x08002b50   0x00000008   Code   RO         1418    i.jy60_get_angle    jy60.o
    0x08002b58   0x08002b58   0x00000008   Code   RO         1419    i.jy60_get_angle_speed  jy60.o
    0x08002b60   0x08002b60   0x00000098   Code   RO         1423    i.jy60_init         jy60.o
    0x08002bf8   0x08002bf8   0x0000001c   Code   RO         1424    i.jy60_send_command  jy60.o
    0x08002c14   0x08002c14   0x00000090   Code   RO         1426    i.jy60_uart_recv_callback  jy60.o
    0x08002ca4   0x08002ca4   0x00000004   Code   RO         1512    i.jyxx_init         jyxx.o
    0x08002ca8   0x08002ca8   0x00000108   Code   RO         1513    i.jyxx_oled_show    jyxx.o
    0x08002db0   0x08002db0   0x00000004   Code   RO         1287    i.key_get_value     key.o
    0x08002db4   0x08002db4   0x00000018   Code   RO         1288    i.key_init          key.o
    0x08002dcc   0x08002dcc   0x00000004   Code   RO         1289    i.key_scan          key.o
    0x08002dd0   0x08002dd0   0x00000038   Code   RO          868    i.led_init          led.o
    0x08002e08   0x08002e08   0x00000010   Code   RO          870    i.led_toggle        led.o
    0x08002e18   0x08002e18   0x000000bc   Code   RO         1741    i.main              main.o
    0x08002ed4   0x08002ed4   0x00000004   Code   RO         1632    i.motor_init        motor.o
    0x08002ed8   0x08002ed8   0x00000054   Code   RO         1635    i.motor_set_speed   motor.o
    0x08002f2c   0x08002f2c   0x00000038   Code   RO         2031    i.my_mem_init       malloc.o
    0x08002f64   0x08002f64   0x0000003c   Code   RO         2033    i.my_mem_perused    malloc.o
    0x08002fa0   0x08002fa0   0x0000004c   Code   RO          917    i.my_printf         debug.o
    0x08002fec   0x08002fec   0x00000060   Code   RO         2779    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x0800304c   0x0800304c   0x00000064   Code   RO         2780    i.prvIdleTask       tasks.o
    0x080030b0   0x080030b0   0x00000100   Code   RO         2781    i.prvListTasksWithinSingleList  tasks.o
    0x080031b0   0x080031b0   0x000000a0   Code   RO         3093    i.prvProcessExpiredTimer  timers.o
    0x08003250   0x08003250   0x000000d4   Code   RO         3094    i.prvSampleTimeNow  timers.o
    0x08003324   0x08003324   0x00000010   Code   RO         2539    i.prvTaskExitError  port.o
    0x08003334   0x08003334   0x000002c8   Code   RO         3095    i.prvTimerTask      timers.o
    0x080035fc   0x080035fc   0x0000006c   Code   RO         2609    i.prvUnlockQueue    queue.o
    0x08003668   0x08003668   0x000000f8   Code   RO         2440    i.pvPortMalloc      heap_4.o
    0x08003760   0x08003760   0x0000002c   Code   RO         2540    i.pxPortInitialiseStack  port.o
    0x0800378c   0x0800378c   0x00000004   Code   RO         2102    i.read_addr         usmart.o
    0x08003790   0x08003790   0x0000012c   Code   RO          194    i.sys_check_rst     sys.o
    0x080038bc   0x080038bc   0x00000128   Code   RO          195    i.sys_clock_set     sys.o
    0x080039e4   0x080039e4   0x0000000c   Code   RO          196    i.sys_get_tick      sys.o
    0x080039f0   0x080039f0   0x00000070   Code   RO          200    i.sys_nvic_init     sys.o
    0x08003a60   0x08003a60   0x00000010   Code   RO          202    i.sys_soft_reset    sys.o
    0x08003a70   0x08003a70   0x00000048   Code   RO          204    i.sys_stm32_clock_init  sys.o
    0x08003ab8   0x08003ab8   0x00000098   Code   RO         1589    i.tb6612_init       tb6612.o
    0x08003b50   0x08003b50   0x0000005c   Code   RO         1590    i.tb6612_set_direction  tb6612.o
    0x08003bac   0x08003bac   0x00000018   Code   RO         1591    i.tb6612_set_pwm_value  tb6612.o
    0x08003bc4   0x08003bc4   0x0000002c   Code   RO          686    i.tim_cap_get_value  tim.o
    0x08003bf0   0x08003bf0   0x0000015c   Code   RO          687    i.tim_cap_irq_callback  tim.o
    0x08003d4c   0x08003d4c   0x00000240   Code   RO          688    i.tim_cap_pin_init  tim.o
    0x08003f8c   0x08003f8c   0x00000274   Code   RO          690    i.tim_cnt_pin_init  tim.o
    0x08004200   0x08004200   0x000000a8   Code   RO          691    i.tim_cnt_set_value  tim.o
    0x080042a8   0x080042a8   0x000000b8   Code   RO          693    i.tim_int_callback_register  tim.o
    0x08004360   0x08004360   0x00000218   Code   RO          694    i.tim_pwm_pin_init  tim.o
    0x08004578   0x08004578   0x000000c4   Code   RO          695    i.tim_pwm_set_ccr   tim.o
    0x0800463c   0x0800463c   0x0000000a   Code   RO         1361    i.track_init        track.o
    0x08004646   0x08004646   0x00000002   PAD
    0x08004648   0x08004648   0x00000070   Code   RO         1362    i.track_oled_show   track.o
    0x080046b8   0x080046b8   0x0000004c   Code   RO         1684    i.ultrasound_init   ultrasound.o
    0x08004704   0x08004704   0x00000040   Code   RO         1686    i.ultrasound_read_distance  ultrasound.o
    0x08004744   0x08004744   0x00000078   Code   RO         1688    i.ultrasound_tim_ic_handler  ultrasound.o
    0x080047bc   0x080047bc   0x00000010   Code   RO         1689    i.ultrasound_tim_overflow_handler  ultrasound.o
    0x080047cc   0x080047cc   0x0000015c   Code   RO          384    i.usart_init        usart.o
    0x08004928   0x08004928   0x0000000c   Code   RO          386    i.usart_iqr_callback_register  usart.o
    0x08004934   0x08004934   0x00000088   Code   RO          388    i.usart_send_data   usart.o
    0x080049bc   0x080049bc   0x000000b0   Code   RO         2103    i.usmart_cmd_rec    usmart.o
    0x08004a6c   0x08004a6c   0x00000280   Code   RO         2104    i.usmart_exe        usmart.o
    0x08004cec   0x08004cec   0x000000b6   Code   RO         2253    i.usmart_get_aparm  usmart_str.o
    0x08004da2   0x08004da2   0x00000042   Code   RO         2254    i.usmart_get_cmdname  usmart_str.o
    0x08004de4   0x08004de4   0x000001b4   Code   RO         2255    i.usmart_get_fname  usmart_str.o
    0x08004f98   0x08004f98   0x00000308   Code   RO         2256    i.usmart_get_fparam  usmart_str.o
    0x080052a0   0x080052a0   0x0000001c   Code   RO         2196    i.usmart_get_input_string  usmart_port.o
    0x080052bc   0x080052bc   0x00000058   Code   RO         2257    i.usmart_get_parmpos  usmart_str.o
    0x08005314   0x08005314   0x00000020   Code   RO         2105    i.usmart_init       usmart.o
    0x08005334   0x08005334   0x00000044   Code   RO         2197    i.usmart_recv_func  usmart_port.o
    0x08005378   0x08005378   0x000000a4   Code   RO         2106    i.usmart_scan       usmart.o
    0x0800541c   0x0800541c   0x00000112   Code   RO         2260    i.usmart_str2num    usmart_str.o
    0x0800552e   0x0800552e   0x0000001a   Code   RO         2261    i.usmart_strcmp     usmart_str.o
    0x08005548   0x08005548   0x0000069c   Code   RO         2107    i.usmart_sys_cmd_exe  usmart.o
    0x08005be4   0x08005be4   0x00000034   Code   RO         2198    i.usmart_timx_get_time  usmart_port.o
    0x08005c18   0x08005c18   0x00000058   Code   RO         2199    i.usmart_timx_init  usmart_port.o
    0x08005c70   0x08005c70   0x00000030   Code   RO         2200    i.usmart_timx_reset_time  usmart_port.o
    0x08005ca0   0x08005ca0   0x00000024   Code   RO         2502    i.uxListRemove      list.o
    0x08005cc4   0x08005cc4   0x00000016   Code   RO         2503    i.vListInitialise   list.o
    0x08005cda   0x08005cda   0x00000006   Code   RO         2504    i.vListInitialiseItem  list.o
    0x08005ce0   0x08005ce0   0x0000003a   Code   RO         2505    i.vListInsert       list.o
    0x08005d1a   0x08005d1a   0x00000018   Code   RO         2506    i.vListInsertEnd    list.o
    0x08005d32   0x08005d32   0x00000002   PAD
    0x08005d34   0x08005d34   0x0000001c   Code   RO         2542    i.vPortEnterCritical  port.o
    0x08005d50   0x08005d50   0x00000018   Code   RO         2543    i.vPortExitCritical  port.o
    0x08005d68   0x08005d68   0x0000008c   Code   RO         2441    i.vPortFree         heap_4.o
    0x08005df4   0x08005df4   0x00000018   Code   RO         2544    i.vPortSetupTimerInterrupt  port.o
    0x08005e0c   0x08005e0c   0x0000004a   Code   RO         2619    i.vQueueWaitForMessageRestricted  queue.o
    0x08005e56   0x08005e56   0x00000002   PAD
    0x08005e58   0x08005e58   0x000000d0   Code   RO         2790    i.vTaskDelete       tasks.o
    0x08005f28   0x08005f28   0x00000010   Code   RO         2794    i.vTaskInternalSetTimeOutState  tasks.o
    0x08005f38   0x08005f38   0x00000184   Code   RO         2795    i.vTaskListTasks    tasks.o
    0x080060bc   0x080060bc   0x0000000c   Code   RO         2796    i.vTaskMissedYield  tasks.o
    0x080060c8   0x080060c8   0x00000020   Code   RO         2797    i.vTaskPlaceOnEventList  tasks.o
    0x080060e8   0x080060e8   0x00000040   Code   RO         2798    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x08006128   0x08006128   0x00000074   Code   RO         2805    i.vTaskStartScheduler  tasks.o
    0x0800619c   0x0800619c   0x00000010   Code   RO         2806    i.vTaskSuspendAll   tasks.o
    0x080061ac   0x080061ac   0x0000004c   Code   RO         2807    i.vTaskSwitchContext  tasks.o
    0x080061f8   0x080061f8   0x00000004   Code   RO         2108    i.write_addr        usmart.o
    0x080061fc   0x080061fc   0x0000000c   Code   RO         2445    i.xPortGetFreeHeapSize  heap_4.o
    0x08006208   0x08006208   0x00000040   Code   RO         2545    i.xPortStartScheduler  port.o
    0x08006248   0x08006248   0x0000002c   Code   RO         2546    i.xPortSysTickHandler  port.o
    0x08006274   0x08006274   0x00000094   Code   RO         2621    i.xQueueGenericCreate  queue.o
    0x08006308   0x08006308   0x00000114   Code   RO         2630    i.xQueueReceive     queue.o
    0x0800641c   0x0800641c   0x00000058   Code   RO         2809    i.xTaskCheckForTimeOut  tasks.o
    0x08006474   0x08006474   0x000001a4   Code   RO         2810    i.xTaskCreate       tasks.o
    0x08006618   0x08006618   0x0000005c   Code   RO         2811    i.xTaskDelayUntil   tasks.o
    0x08006674   0x08006674   0x0000001c   Code   RO         2818    i.xTaskGetSchedulerState  tasks.o
    0x08006690   0x08006690   0x0000000c   Code   RO         2819    i.xTaskGetTickCount  tasks.o
    0x0800669c   0x0800669c   0x00000178   Code   RO         2821    i.xTaskIncrementTick  tasks.o
    0x08006814   0x08006814   0x000000e8   Code   RO         2824    i.xTaskRemoveFromEventList  tasks.o
    0x080068fc   0x080068fc   0x00000154   Code   RO         2825    i.xTaskResumeAll    tasks.o
    0x08006a50   0x08006a50   0x00000070   Code   RO         3104    i.xTimerCreateTimerTask  timers.o
    0x08006ac0   0x08006ac0   0x00000480   Data   RO           29    .constdata          gpio.o
    0x08006f40   0x08006f40   0x0000001a   Data   RO          697    .constdata          tim.o
    0x08006f5a   0x08006f5a   0x00000008   Data   RO          871    .constdata          led.o
    0x08006f62   0x08006f62   0x000005f0   Data   RO         1196    .constdata          oled_data.o
    0x08007552   0x08007552   0x0000023a   Data   RO         1197    .constdata          oled_data.o
    0x0800778c   0x0800778c   0x000000f5   Data   RO         1198    .constdata          oled_data.o
    0x08007881   0x08007881   0x00000012   Data   RO         1242    .constdata          gpio_key.o
    0x08007893   0x08007893   0x00000006   Data   RO         1325    .constdata          encoder.o
    0x08007899   0x08007899   0x0000000a   Data   RO         1592    .constdata          tb6612.o
    0x080078a3   0x080078a3   0x0000000d   Data   RO         1820    .constdata          app_key.o
    0x080078b0   0x080078b0   0x0000000c   Data   RO         2040    .constdata          malloc.o
    0x080078bc   0x080078bc   0x00000028   Data   RO         2109    .conststring        usmart.o
    0x080078e4   0x080078e4   0x000000dc   Data   RO         2169    .conststring        usmart_config.o
    0x080079c0   0x080079c0   0x00000020   Data   RO         4434    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080079e0, Size: 0x000105f0, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x0000007c])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000008   Data   RW          205    .data               sys.o
    0x20000008   COMPRESSED   0x00000004   Data   RW          342    .data               delay.o
    0x2000000c   COMPRESSED   0x00000008   Data   RW          486    .data               i2c.o
    0x20000014   COMPRESSED   0x00000004   Data   RW          698    .data               tim.o
    0x20000018   COMPRESSED   0x0000000a   Data   RW         1243    .data               gpio_key.o
    0x20000022   COMPRESSED   0x00000001   Data   RW         1365    .data               track.o
    0x20000023   COMPRESSED   0x00000001   PAD
    0x20000024   COMPRESSED   0x00000010   Data   RW         1429    .data               jy60.o
    0x20000034   COMPRESSED   0x00000004   Data   RW         1636    .data               motor.o
    0x20000038   COMPRESSED   0x00000002   Data   RW         1821    .data               app_key.o
    0x2000003a   COMPRESSED   0x00000002   Data   RW         1892    .data               app_state.o
    0x2000003c   COMPRESSED   0x00000001   Data   RW         1933    .data               app_ui.o
    0x2000003d   COMPRESSED   0x00000003   PAD
    0x20000040   COMPRESSED   0x00000014   Data   RW         2041    .data               malloc.o
    0x20000054   COMPRESSED   0x0000001c   Data   RW         2110    .data               usmart.o
    0x20000070   COMPRESSED   0x00000030   Data   RW         2170    .data               usmart_config.o
    0x200000a0   COMPRESSED   0x000000f4   Data   RW         2171    .data               usmart_config.o
    0x20000194   COMPRESSED   0x00000002   Data   RW         2202    .data               usmart_port.o
    0x20000196   COMPRESSED   0x00000002   PAD
    0x20000198   COMPRESSED   0x0000001c   Data   RW         2448    .data               heap_4.o
    0x200001b4   COMPRESSED   0x00000004   Data   RW         2547    .data               port.o
    0x200001b8   COMPRESSED   0x00000040   Data   RW         2827    .data               tasks.o
    0x200001f8   COMPRESSED   0x00000014   Data   RW         3113    .data               timers.o
    0x2000020c        -       0x00000040   Zero   RW           28    .bss                gpio.o
    0x2000024c        -       0x0000001c   Zero   RW          389    .bss                usart.o
    0x20000268        -       0x000000e4   Zero   RW          696    .bss                tim.o
    0x2000034c        -       0x00000420   Zero   RW          918    .bss                debug.o
    0x2000076c        -       0x00000400   Zero   RW         1018    .bss                oled.o
    0x20000b6c        -       0x00000066   Zero   RW         1428    .bss                jy60.o
    0x20000bd2   COMPRESSED   0x00000002   PAD
    0x20000bd4        -       0x00000014   Zero   RW         1690    .bss                ultrasound.o
    0x20000be8        -       0x00000028   Zero   RW         1819    .bss                app_key.o
    0x20000c10        -       0x00000100   Zero   RW         1891    .bss                app_state.o
    0x20000d10   COMPRESSED   0x00000030   PAD
    0x20000d40        -       0x00009940   Zero   RW         2038    .bss                malloc.o
    0x2000a680        -       0x00000994   Zero   RW         2039    .bss                malloc.o
    0x2000b014        -       0x00000100   Zero   RW         2201    .bss                usmart_port.o
    0x2000b114        -       0x00005000   Zero   RW         2447    .bss                heap_4.o
    0x20010114        -       0x000000b4   Zero   RW         2826    .bss                tasks.o
    0x200101c8        -       0x00000028   Zero   RW         3112    .bss                timers.o
    0x200101f0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0        648   adc_key.o
       240         66         13          2         40       4878   app_key.o
       436        230          0          2        256       2040   app_state.o
       188         50          0          1          0       1427   app_ui.o
       756        182          0          0       1056       5435   debug.o
       144         14          0          4          0       2451   delay.o
        60          6          6          0          0       1240   encoder.o
         0          0          0          0          0      15340   event_groups.o
      1100         90       1152          0         64     571457   gpio.o
       392         26         18         10          0       2965   gpio_key.o
       400         22          0         28      20480       4886   heap_4.o
       692         36          0          8          0       5723   i2c.o
       348         34          0         16        102       4548   jy60.o
       268         96          0          0          0       1909   jyxx.o
        32          6          0          0          0       1810   key.o
        72          8          8          0          0       1753   led.o
       146          0          0          0          0       3458   list.o
       464        252          0          0          0      32273   main.o
       116         16         12         20      41684       3382   malloc.o
        88          4          0          4          0       2425   motor.o
      1418         34          0          0       1024       8438   oled.o
         0          0       2335          0          0       1110   oled_data.o
       434         52          0          4          0       9684   port.o
       606          4          0          0          0       9572   queue.o
        44         10        392          0       1024        956   startup_stm32f407xx.o
       964        312          0          8          0      22087   sys.o
      2968        240          0         64        180      37348   tasks.o
       268         24         10          0          0       2905   tb6612.o
      3012        478         26          4        228      15344   tim.o
      1196         56          0         20         40      14130   timers.o
       122         40          0          1          0       1844   track.o
       276         48          0          0         20       3034   ultrasound.o
       744        134          0          0         28       6553   usart.o
      2712       1312         40         28          0       6622   usmart.o
         0          0        220        292          0       1448   usmart_config.o
       344         82          0          2        256       4125   usmart_port.o
      1858         22          0          0          0      11171   usmart_str.o

    ----------------------------------------------------------------------
     22918       <USER>       <GROUP>        524      66532     826419   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        10          0          0          6         50          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2358         96          0          0          0        764   printfa.o
        28          0          0          0          0         76   strcmp.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o

    ----------------------------------------------------------------------
      4018        <USER>          <GROUP>          0          0       2320   Library Totals
         8          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2906        112          0          0          0       1596   mc_w.l
      1104          0          0          0          0        724   mf_w.l

    ----------------------------------------------------------------------
      4018        <USER>          <GROUP>          0          0       2320   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     26936       4098       4264        524      66532     810875   Grand Totals
     26936       4098       4264        124      66532     810875   ELF Image Totals (compressed)
     26936       4098       4264        124          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                31200 (  30.47kB)
    Total RW  Size (RW Data + ZI Data)             67056 (  65.48kB)
    Total ROM Size (Code + RO Data + RW Data)      31324 (  30.59kB)

==============================================================================

