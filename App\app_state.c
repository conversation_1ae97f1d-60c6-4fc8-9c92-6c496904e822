#include "../System/config.h"

#include "./BSP/LED/led.h"
#include "./BSP/DEBUG/debug.h"

#include "./MALLOC/malloc.h"

#include "FreeRTOS.h"
#include "task.h"

#include "app_state.h"

uint8_t is_app_state_task_start = 0;
uint8_t sys_state = 0x00;
char task_log[256];
/**
 * @brief       状态监测任务
 * @param       arg: 传入的参数
 * @retval      无
 */
static void app_state_task(void *arg)
{
    uint8_t cnt = 10;
    TickType_t tick;
    tick = xTaskGetTickCount();
    while (is_app_state_task_start)
    {
#if CFG_IWDG_EN == CFG_ENABLE
        iwdg_feed();
#endif
        debug_printf("distan:%.2f \r\n", ultrasound_read_distance());
        if (sys_state == 0x00)
        {
            led_toggle(4); // 切换LED1状态
            led_toggle(1); // 切换LED1状态
        }

        vTaskDelayUntil(&tick, 500);
        if (++cnt < 10)
            continue;
        cnt = 0;
#if CFG_DEBUG_SYS_LOG == CFG_ENABLE
        debug_printf("********************************************\r\n");

        uint16_t memused = my_mem_perused(SRAMIN); /* 获取内存使用率 */
        debug_printf("内存占用率 :%d.%01d%%\r\n", memused / 10, memused % 1);

        memused = xPortGetFreeHeapSize();
        debug_printf("系统空闲堆大小 :%d\r\n", memused);

        vTaskList(task_log);
        my_printf("B:阻塞  R:就绪  D:删除  S:暂停  X:运行 \r\n");
        my_printf("任务名\t\t\t状态\t优先级\t剩余栈\t序号\r\n");
        my_printf("%s", task_log);

        debug_printf("********************************************\r\n");
#endif
    }
    vTaskDelete(NULL);
}

/**
 * @brief       启动状态监测任务
 * @param       无
 * @retval      1:失败 0:成功
 */
uint8_t app_state_task_start(void)
{
    if (is_app_state_task_start)
        return 1;

    if (xTaskCreate(app_state_task, "app_state_task", 256, NULL, 1, NULL) != pdPASS)
    {
        return 1;
    }
    is_app_state_task_start = 1;
    return 0;
}

/**
 * @brief       停止状态监测任务
 * @param       无
 * @retval      1:失败 0:成功
 */
void app_state_task_stop(void)
{
    is_app_state_task_start = 0;
}
