#ifndef __LED_H_
#define __LED_H_

#include "./PORT/port.h"

/**
 * @brief       初始化LED
 * @param       无
 * @retval      无
 */
void led_init(void);

/**
 * @brief       设置LED状态
 * @param       led_id: LED编号, 0~3
 * @param       state: LED状态, 0:亮, 1:灭
 * @retval      无
 */
void led_set(uint8_t led_id, uint8_t state);

/**
 * @brief       切换LED状态
 * @param       led_id: LED编号
 * @retval      无
 */
void led_toggle(uint8_t led_id);

#endif /* __LED_H_ */
