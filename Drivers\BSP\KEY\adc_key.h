#ifndef __ADC_KEY_H_
#define __ADC_KEY_H_

#include "./BSP/KEY/key.h"

/**
 * @breif   adc按键初始化
 * @param   无
 * @retval  无
 */
void adc_key_init(void);

/**
 * @breif   读取adc按键值
 * @param   无
 * @retval  按键值
 */
uint8_t adc_key_read(void);

/**
 * @breif   adc按键扫描
 * @param   无
 * @retval  无
 */
void adc_key_scan(void);

/**
 * @breif   获取adc按键值
 * @param   state 按键状态结构体指针
 * @retval  按键值
 */
void adc_key_get_state(key_state_t *state);

#endif
