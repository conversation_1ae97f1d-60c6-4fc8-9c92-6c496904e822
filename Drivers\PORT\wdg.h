#ifndef __WDG_H__
#define __WDG_H__

/**
 * @brief       初始化独立看门狗
 * @param       prer: 分频数:0~7(只有低3位有效!)
 *   @arg       分频因子 = 4 * 2^prer. 但最大值只能是256!
 * @param       rlr: 自动重装载值,0~0XFFF.
 * @note        时间计算(大概):Tout=((4 * 2^prer) * rlr) / 40 (ms).
 * @retval      无
 */
void iwdg_init(uint8_t prer, uint16_t rlr);

/**
 * @brief       喂独立看门狗
 * @param       无
 * @retval      无
 */
void iwdg_feed(void);

#endif
