#include "./BSP/LED/led.h"
#include "./PORT/port.h"
#include "../System/config.h"

#if CFG_LED_EN == 1

const uint8_t led_pin[] = {CFG_LED_PIN};
const uint8_t led_polarity[] = {CFG_LED_POLARITY};

/**
 * @brief       初始化LED
 * @param       无
 * @retval      无
 */
void led_init(void)
{
    for (uint8_t i = 0; i < sizeof(led_pin); i++)
    {
        gpio_pin_init(led_pin[i], GPIO_MODE_OUT, GPIO_OTYPE_PP, GPIO_SPEED_LOW, GPIO_PUPD_PU);
        led_set(i + 1, CFG_LED_OFF);
    }
}

/**
 * @brief       设置LED状态
 * @param       led_id: LED编号
 * @param       state: LED状态, 0:亮, 1:灭
 * @retval      无
 */
void led_set(uint8_t led_id, uint8_t state)
{
    led_id--;
    if (state == CFG_LED_ON)
    {
        gpio_write_pin(led_pin[led_id], led_polarity[led_id]);
    }
    else
    {
        gpio_write_pin(led_pin[led_id], !led_polarity[led_id]);
    }
}

/**
 * @brief       切换LED状态
 * @param       led_id: LED编号
 * @retval      无
 */
void led_toggle(uint8_t led_id)
{
    led_id--;
    gpio_toggle_pin(led_pin[led_id]);
}

#endif
