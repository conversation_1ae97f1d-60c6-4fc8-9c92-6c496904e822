#ifndef __GPIO_KEY_H_
#define __GPIO_KEY_H_

#include "./BSP/KEY/key.h"

/**
 * @breif   初始化按键GPIO
 * @param   无
 * @retval  无
 */
void gpio_key_init(void);

/**
 * @breif   读取按键GPIO状态
 * @param   无
 * @retval  按键值
 */
uint8_t gpio_key_read(void);

/**
 * @breif   gpio按键扫描
 * @param   无
 * @retval  无
 */
void gpio_key_scan(void);

/**
 * @breif   获取gpio按键值
 * @param   state 按键状态结构体指针
 * @retval  按键值
 */
void gpio_key_get_state(key_state_t *state);

#endif
