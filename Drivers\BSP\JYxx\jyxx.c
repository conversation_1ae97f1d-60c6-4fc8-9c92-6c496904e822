#include "./PORT/port.h"
#include "../System/config.h"

#include "./BSP/JYxx/jy60.h"
#include "./BSP/JYxx/jyxx.h"

/**
 * @brief       JYxx初始化
 * @param       无
 * @retval      无
 */
void jyxx_init(void)
{
#if CFG_JYxx_MODULE == 1
    jy60_init();
#elif CFG_JYxx_MODULE == 2

#endif
}

/**
 * @brief       设置JYxx休眠或唤醒
 * @param       state: 休眠或唤醒   1:休眠   0:唤醒
 * @retval      无
 */
void jyxx_set_sleep_wake(uint8_t state)
{
#if CFG_JYxx_MODULE == 1
    jy60_set_sleep_wake(state);
#elif CFG_JYxx_MODULE == 2

#endif
}

/**
 * @brief       更新JYxx数据
 * @param       无
 * @retval      无
 */
void jyxx_up_data(void)
{
#if CFG_JYxx_MODULE == 1
    jy60_up_data();
#elif CFG_JYxx_MODULE == 2

#endif
}

/**
 * @brief       获取JYxx加速度
 * @param       加速度
 * @retval
 */
float *jyxx_get_acceleration(void)
{
#if CFG_JYxx_MODULE == 1
    return jy60_get_acceleration();
#elif CFG_JYxx_MODULE == 2

#endif
}

/**
 * @brief       获取JYxx角速度
 * @param       无
 * @retval      角速度
 */
float *jyxx_get_angle_speed(void)
{
#if CFG_JYxx_MODULE == 1
    return jy60_get_angle_speed();
#elif CFG_JYxx_MODULE == 2

#endif
}

/**
 * @brief       获取JYxx角度
 * @param       无
 * @retval      角度
 */
float *jyxx_get_angle(void)
{
#if CFG_JYxx_MODULE == 1
    return jy60_get_angle();
#elif CFG_JYxx_MODULE == 2

#endif
}

/**
 * @brief       获取JYxx温度
 * @param       无
 * @retval      温度
 */
float jyxx_get_temperature(void)
{
#if CFG_JYxx_MODULE == 1
    return jy60_get_temperature();
#elif CFG_JYxx_MODULE == 2

#endif
}

/**
 * @brief       获取JYxx电压
 * @param       无
 * @retval      电压
 */
float jyxx_get_voltage(void)
{
#if CFG_JYxx_MODULE == 1
    return jy60_get_voltage();
#elif CFG_JYxx_MODULE == 2

#endif
}

/**
 * @brief       获取JYxx版本
 * @param       无
 * @retval      版本
 */
float jyxx_get_version(void)
{
#if CFG_JYxx_MODULE == 1
    return jy60_get_version();
#elif CFG_JYxx_MODULE == 2

#endif
}

/**
 * @brief       JYxx数据在OLED上显示
 * @param       无
 * @retval      无
 */
void jyxx_oled_show(void)
{
#include "./BSP/OLED/OLED.h"
    float *Axyz = jyxx_get_acceleration();
    OLED_Printf(0, 8 * 2, OLED_6X8, "JSD:%4.1f %4.1f %4.1f        ", Axyz[0], Axyz[1], Axyz[2]);
    float *Wxyz = jyxx_get_angle_speed();
    OLED_Printf(0, 8 * 3, OLED_6X8, "JD :%4.1f %4.1f %4.1f        ", Wxyz[0], Wxyz[1], Wxyz[2]);
    float *Grpy = jyxx_get_angle();
    OLED_Printf(0, 8 * 4, OLED_6X8, "OLJ:%4.1f %4.1f %4.1f        ", Grpy[0], Grpy[1], Grpy[2]);
}
