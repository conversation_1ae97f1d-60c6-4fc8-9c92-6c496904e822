# HC-SR04超声波驱动使用说明

## 概述
本驱动基于您提供的HC-SR04超声波传感器驱动代码，已完全移植到当前的STM32F407工程架构中。

## 硬件连接
- **TRIG引脚**: PB8 (触发引脚，输出模式)
- **ECHO引脚**: PB9 (回声引脚，输入捕获模式，连接到TIM11_CH1)

## 配置说明
在 `System/config.h` 中的配置项：
```c
#define CFG_ULTRASOUND_EN               CFG_ENABLE      // 超声波模块使能
#define CFG_ULTRASOUND_ECHO_TIM_ID      11              // 回声引脚定时器号
#define CFG_ULTRASOUND_ECHO_TIM_CHANNEL 1               // 回声引脚定时器通道
#define CFG_ULTRASOUND_ECHO_TIM_PSC     168-1           // 回声引脚定时器预分频PSC
#define CFG_ULTRASOUND_ECHO_TIM_ARR     65535           // 回声引脚定时器重装载ARR
#define CFG_ULTRASOUND_ECHO_PIN         PB9             // 回声引脚
#define CFG_ULTRASOUND_TRIG_PIN         PB8             // 触发引脚
```

## API接口

### 初始化函数
```c
void ultrasound_init(void);
```
初始化超声波模块，配置GPIO和定时器。

### 测距函数
```c
float ultrasound_read_distance(void);
```
触发测距并返回距离值，单位为厘米。

```c
void ultrasound_start(void);
```
仅触发测距，不等待结果。

```c
float ultrasound_read(void);
```
读取上次测量的距离值。

## 使用示例

### 基本使用
```c
#include "./BSP/ULTRASOUND/ultrasound.h"

int main(void)
{
    // 系统初始化
    sys_init();
    
    // 初始化超声波
    ultrasound_init();
    
    while(1)
    {
        // 读取距离
        float distance = ultrasound_read_distance();
        printf("距离: %.2f cm\r\n", distance);
        delay_ms(1000);
    }
}
```

### 测试函数
```c
#include "./BSP/ULTRASOUND/ultrasound_test.h"

// 在main函数中调用
ultrasound_test();  // 连续测试
// 或
float dist = ultrasound_single_test();  // 单次测试
```

## 技术特点
1. **输入捕获测量**: 使用TIM11的输入捕获功能精确测量回声时间
2. **中断驱动**: 基于定时器中断处理，不占用CPU时间
3. **溢出处理**: 支持定时器溢出处理，可测量较远距离
4. **精度优化**: 使用浮点运算，测量精度高
5. **架构兼容**: 完全符合当前工程的BSP架构设计

## 测量范围
- **最小距离**: 2cm
- **最大距离**: 450cm
- **精度**: ±1cm

## 注意事项
1. 确保CFG_ULTRASOUND_EN设置为CFG_ENABLE
2. TIM11必须配置为输入捕获模式
3. 测量间隔建议不少于60ms
4. 超出测量范围时返回450cm

## 移植说明
本驱动已从您的原始HAL库代码完全移植到当前的PORT层架构：
- 使用PORT层的GPIO和定时器接口
- 集成到BSP驱动架构中
- 保持原有的测量算法和精度
- 添加了完整的配置管理和错误处理
