#include "./BSP/ULTRASOUND/ultrasound.h"
#include "./PORT/port.h"
#include "../System/config.h"

#if CFG_ULTRASOUND_EN == 1

// HC-SR04超声波传感器信息结构体
static Hcsr04InfoTypeDef Hcsr04Info;

/**
 * @brief       初始化超声波模块
 * @param       无
 * @retval      无
 */
void ultrasound_init(void)
{
    // 初始化ECHO引脚为输入捕获模式
    tim_cap_pin_init(CFG_ULTRASOUND_ECHO_TIM_ID, CFG_ULTRASOUND_ECHO_TIM_CHANNEL, CFG_ULTRASOUND_ECHO_PIN, CFG_ULTRASOUND_ECHO_TIM_ARR, CFG_ULTRASOUND_ECHO_TIM_PSC);

    // 初始化TRIG引脚为输出模式
    gpio_pin_init(CFG_ULTRASOUND_TRIG_PIN, GPIO_MODE_OUT, GPIO_OTYPE_PP, GPIO_SPEED_HIGH, GPIO_PUPD_NONE);
    gpio_write_pin(CFG_ULTRASOUND_TRIG_PIN, 0); // 初始化为低电平

    // 初始化HC-SR04信息结构体
    Hcsr04Info.edge_state = 0;
    Hcsr04Info.tim_overflow_counter = 0;
    Hcsr04Info.t1 = 0;
    Hcsr04Info.t2 = 0;
    Hcsr04Info.high_level_us = 0;
    Hcsr04Info.distance = 0.0f;
}

/**
 * @brief       触发超声波测距并读取距离（可以300ms调用一次）
 * @param       无
 * @retval      距离值(单位:cm)
 */
float ultrasound_read_distance(void)
{
    ultrasound_start();
    return ultrasound_read();
}

/**
 * @brief       触发超声波测距
 * @param       无
 * @retval      无
 */
void ultrasound_start(void)
{
    gpio_write_pin(CFG_ULTRASOUND_TRIG_PIN, 1);
    delay_us(12); // 延时12us，满足>10us的要求
    gpio_write_pin(CFG_ULTRASOUND_TRIG_PIN, 0);
}

/**
 * @brief       读取距离值
 * @param       无
 * @retval      距离值(单位:cm)
 */
float ultrasound_read(void)
{
    // 测距结果限幅
    if (Hcsr04Info.distance >= 450.0f)
    {
        Hcsr04Info.distance = 450.0f;
    }
    return Hcsr04Info.distance;
}

/**
 * @brief       定时器溢出中断处理函数
 * @param       无
 * @retval      无
 * @note        在定时器溢出中断中调用此函数
 */
void ultrasound_tim_overflow_handler(void)
{
    // if (Hcsr04Info.edge_state & 0x40) // 已经捕获到高电平了
    // {
    //     if ((Hcsr04Info.tim_overflow_counter & 0x3F) == 0x3F) // 高电平太长了
    //     {
    //         Hcsr04Info.edge_state |= 0x80; // 标记成功捕获了一次
    //         Hcsr04Info.high_level_us = 0xFFFFFFFF;
    //     }
    //     else // 还可以累加高电平长度
    //     {
    Hcsr04Info.tim_overflow_counter++;
    //     }
    // }
}

/**
 * @brief       输入捕获中断处理函数
 * @param       无
 * @retval      无
 * @note        在输入捕获中断中调用此函数
 */
void ultrasound_tim_ic_handler(void)
{
    if (Hcsr04Info.edge_state == 0) // 捕获上升沿
    {
        // 得到上升沿开始时间T1，并更改输入捕获为下降沿
        Hcsr04Info.t1 = tim_cap_get_value(CFG_ULTRASOUND_ECHO_TIM_ID);
        Hcsr04Info.tim_overflow_counter = 0; // 定时器溢出计数器清零
        Hcsr04Info.edge_state = 1;           // 上升沿、下降沿捕获标志位
    }
    else if (Hcsr04Info.edge_state == 1) // 捕获下降沿
    {
        // 捕获下降沿时间T2，并计算高电平时间
        Hcsr04Info.t2 = tim_cap_get_value(CFG_ULTRASOUND_ECHO_TIM_ID);
        Hcsr04Info.t2 += Hcsr04Info.tim_overflow_counter * CFG_ULTRASOUND_ECHO_TIM_ARR; // 需要考虑定时器溢出中断
        Hcsr04Info.high_level_us = Hcsr04Info.t2 - Hcsr04Info.t1;                       // 高电平持续时间 = 下降沿时间点 - 上升沿时间点

        // 计算距离：距离(cm) = (高电平时间(us) / 1000000) * 340 / 2 * 100
        // 简化为：距离(cm) = 高电平时间(us) * 0.017
        Hcsr04Info.distance = (Hcsr04Info.high_level_us / 1000000.0f) * 340.0f / 2.0f * 100.0f;

        // 重新开启上升沿捕获
        Hcsr04Info.edge_state = 0; // 一次采集完毕，清零
    }
}

#endif
