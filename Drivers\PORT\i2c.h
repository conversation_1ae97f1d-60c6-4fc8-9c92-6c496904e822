#ifndef __I2C_H
#define __I2C_H

/**
 * @brief       初始化I2C
 * @param       i2c_id: I2C接口ID号
 * @param       sda: SDA引脚
 * @param       scl: SCL引脚
 * @retval      无
 */
void i2c_init(uint8_t i2c_id, uint8_t sda, uint8_t scl);

/**
 * @brief       产生I2C起始信号
 * @param       i2c_id: I2C接口ID号
 * @retval      无
 */
void i2c_start(uint8_t i2c_id);

/**
 * @brief       产生I2C停止信号
 * @param       i2c_id: I2C接口ID号
 * @retval      无
 */
void i2c_stop(uint8_t i2c_id);

/**
 * @brief       I2C延时函数,用于控制I2C读写速度
 * @param       i2c_id: I2C接口ID号
 * @retval      无
 */
void i2c_delay(uint8_t i2c_id);

/**
 * @brief       等待应答信号到来
 * @param       i2c_id: I2C接口ID号
 * @retval      1，接收应答失败
 *              0，接收应答成功
 */
uint8_t i2c_wait_ack(uint8_t i2c_id);

/**
 * @brief       产生ACK应答
 * @param       i2c_id: I2C接口ID号
 * @retval      无
 */
void i2c_ack(uint8_t i2c_id);

/**
 * @brief       不产生ACK应答
 * @param       i2c_id: I2C接口ID号
 * @retval      无
 */
void i2c_nack(uint8_t i2c_id);

/**
 * @brief       I2C发送一个字节
 * @param       i2c_id: I2C接口ID号
 * @param       data: 要发送的数据
 * @retval      无
 */
void i2c_send_byte(uint8_t i2c_id, uint8_t data);

/**
 * @brief       I2C读取一个字节
 * @param       i2c_id: I2C接口ID号
 * @param       ack:  ack=1时，发送ack; ack=0时，发送nack
 * @retval      接收到的数据
 */
uint8_t i2c_read_byte(uint8_t i2c_id, uint8_t ack);

/**
 * @brief       I2C读取数据
 * @param       i2c_id: I2C接口ID号
 * @param       salve_adress: 从设备地址
 * @param       reg_address: 寄存器地址
 * @param       data: 存储读取数据的缓冲区
 * @param       len: 要读取的数据长度
 * @retval      1，读取成功；0，读取失败
 */
uint8_t i2c_read_data(uint8_t i2c_id, uint8_t salve_adress, uint8_t reg_address, uint8_t *data, uint8_t len);

/**
 * @brief       I2C写入数据
 * @param       i2c_id: I2C接口ID号
 * @param       salve_adress: 从设备地址
 * @param       reg_address: 寄存器地址
 * @param       data: 存储读取数据的缓冲区
 * @param       len: 要读取的数据长度
 * @retval      1，读取成功；0，读取失败
 */
void i2c_write_data(uint8_t i2c_id, uint8_t salve_adress, uint8_t reg_address, uint8_t *data, uint8_t len);

#endif /* __I2C_H */
