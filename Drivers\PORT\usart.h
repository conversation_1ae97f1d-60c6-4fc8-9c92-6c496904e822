/**
 ****************************************************************************************************
 * @file        usart.h
 * <AUTHOR>
 * @version     V1.0
 * @date        2020-04-17
 * @brief       串口初始化代码(一般是串口1)，支持printf
 * @license     Copyright (c) 2020-2032, 广州市星翼电子科技有限公司
 ****************************************************************************************************
 * @attention
 *
 * 实验平台:正点原子 STM32F103开发板
 * 在线视频:www.yuanzige.com
 * 技术论坛:www.openedv.com
 * 公司网址:www.alientek.com
 * 购买地址:openedv.taobao.com
 *
 * 修改说明
 * V1.0 20200417
 * 第一次发布
 *
 ****************************************************************************************************
 */

#ifndef __USART_H
#define __USART_H

#include "./PORT/port.h"

/**
 * @brief       串口初始化
 * @param       usartx:串口号, 1~5
 * @param       baudrate:波特率
 * @param       txpin:发送引脚
 * @param       rxpin:接收引脚
 * @param       is_rx:是否使能接收
 * @retval      无
 * */
void usart_init(uint8_t usartx, uint32_t baudrate, uint8_t txpin, uint8_t rxpin, uint8_t is_rx);

/**
 * @brief       串口发送一个字节
 * @param       usartx:串口号, 1~5
 * @param       data:要发送的数据
 * @retval      无
 * */
void usart_send_byte(uint8_t usartx, uint8_t data);

/**
 * @brief       串口发送多个字节
 * @param       usartx:串口号, 1~5
 * @param       data:要发送的数据
 * @param       len:要发送的数据长度
 * @retval      无
 * */
void usart_send_data(uint8_t usartx, uint8_t *data, uint16_t len);

/**
 * @brief       串口中断回调函数注册
 * @param       callback:回调函数
 * @retval      无
 */
void usart_iqr_callback_register(uint8_t usart_id, void (*callback)(uint8_t));

/**
 * @brief       串口中断回调函数注销
 * @param       callback:回调函数
 * @retval      无
 */
void usart_iqr_callback_deregister(uint8_t usart_id, void (*callback)(uint8_t));

#endif
